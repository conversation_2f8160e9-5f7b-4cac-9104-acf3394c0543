package com.zxing.view;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.View;
import android.view.ViewTreeObserver;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.google.zxing.Result;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.utils.UiUtils;
import com.ybmmarket20.R;
import com.ybmmarket20.common.util.ConvertUtils;
import com.zxing.camera.CameraManager;
import com.zxing.decoding.BeepManager;
import com.zxing.decoding.ScannerViewHandler;

import java.io.IOException;


/**
 * Created by ybm on 2017/6/26.
 * 扫描页面的布局封装成一个view
 */

public class ScannerView extends RelativeLayout implements SurfaceHolder.Callback, View.OnClickListener {
    public static final int CLICK_TAG_CONFIRM = 1;

    private SurfaceView surfaceView;//预览
    private MarkerView markerView;//遮盖框
    private View flMode;//扫码和输入
    private TextView tvMode;//模式切换的文字
    private EditText etInput;//输入框
    private TextView tvConfirm;//确认按钮
    private View llSwitchLight;//灯光
    private ImageView ivLaser;//扫描线
    private CheckBox cbLight;//灯光
    private TextView tvLightTip;//当前灯光状态

    private ScannerViewHandler handler;
    private BeepManager mBeepManager;
    private Animation mLaserAnim;
    private boolean isInput;//是否是输入
    private boolean hasSurface = false;//已经打开了预览
    private ScanListener mListener;
    private ClickListener mClickListener;

    /**
     * 扫描返回的接口
     */
    public interface ScanListener {
        void decodeSuccess(Result result, Bitmap bitmap);

        void returnIntent(Intent intent);

        void returnMessage(String message);
    }

    /**
     * 点击事件的接口
     */
    public interface ClickListener {
        void onClick(int tag, String content);
    }

    public ScannerView(Context context) {
        this(context, null);
    }

    public ScannerView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    private void init(Context context) {
        final View view = LayoutInflater.from(context).inflate(R.layout.layout_scanner_view, null);
        surfaceView = (SurfaceView) view.findViewById(R.id.surface_view);
        markerView = (MarkerView) view.findViewById(R.id.marker_view);
        flMode = view.findViewById(R.id.fl_mode_tip);
        tvMode = (TextView) view.findViewById(R.id.tv_mode);
        tvConfirm = (TextView) view.findViewById(R.id.tv_confirm);
        etInput = (EditText) view.findViewById(R.id.edit_text);
        ivLaser = (ImageView) view.findViewById(R.id.iv_laser);
        llSwitchLight = view.findViewById(R.id.ll_switch_light);
        cbLight = (CheckBox) view.findViewById(R.id.cb_light);
        tvLightTip = (TextView) view.findViewById(R.id.tv_light_tip);

        addView(view);
        flMode.setEnabled(false);
        flMode.setOnClickListener(this);
        tvConfirm.setOnClickListener(this);
        cbLight.setOnClickListener(this);
        markerView.setChangeListener(new MarkerView.ChangeListener() {
            @Override
            public void start(boolean isExpand) {
                if (!isExpand) {
                    llSwitchLight.setVisibility(GONE);
                } else {
                    etInput.setVisibility(GONE);
                    tvConfirm.setVisibility(GONE);
                    etInput.requestFocus();
                }
            }

            @Override
            public void end(boolean isExpand) {
                if (isExpand) {
                    llSwitchLight.setVisibility(VISIBLE);
                } else {
                    tvConfirm.setVisibility(VISIBLE);
                    etInput.setVisibility(VISIBLE);
                }
            }
        });
        setAllViewLocation();
    }

    public void setScanListener(ScanListener listener) {
        mListener = listener;
    }

    public void setClickListener(ClickListener listener) {
        mClickListener = listener;
    }

    public void switchModeEnable(boolean enable) {
        flMode.setClickable(enable);
        flMode.setEnabled(enable);
        tvMode.setText(enable ? "手动输入条形码" : "请将条码对准扫描框");
    }

    @Override
    public void surfaceCreated(SurfaceHolder holder) {
        if (!hasSurface) {
            hasSurface = true;
            initCamera(holder);
        }
    }

    @Override
    public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {

    }

    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        hasSurface = false;
        holder.removeCallback(this);
    }

    /**
     * 从新设置每个view的相对位置
     */
    private void setAllViewLocation() {
        getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                if (markerView == null) {
                    return;
                }
                Rect rect = markerView.mRectFrame;
                if (rect == null || rect.left == 0) {
                    int width = UiUtils.getScreenWidth();
                    int height = UiUtils.getScreenHeight();
                    int w = ConvertUtils.dp2px(280);
                    int h = ConvertUtils.dp2px(180);
                    rect = new Rect((width - w) / 2, (height - h) / 2, (width + w) / 2, (height + h) / 2);
                }

                LayoutParams params = (LayoutParams) flMode.getLayoutParams();
                params.setMargins(0, rect.top - 2 * flMode.getMeasuredHeight(), 0, 0);
                flMode.setLayoutParams(params);

                params = (LayoutParams) etInput.getLayoutParams();
                params.setMargins(rect.left, rect.top, rect.left, 0);
                etInput.setLayoutParams(params);

                params = (LayoutParams) tvConfirm.getLayoutParams();
                params.setMargins(0, rect.top + 300, 0, 0);
                tvConfirm.setLayoutParams(params);

                params = (LayoutParams) llSwitchLight.getLayoutParams();
                params.setMargins(0, rect.bottom + 100, 0, 0);
                llSwitchLight.setLayoutParams(params);

//                params = (LayoutParams) ivLaser.getLayoutParams();
//                params.setMargins(0, rect.top, 0, 0);
//                ivLaser.setLayoutParams(params);
//                laserMove(true, rect.bottom - rect.top - 25);

                getViewTreeObserver().removeOnGlobalLayoutListener(this);
            }
        });
    }

    /**
     * 扫描线移动
     *
     * @param loop
     * @param moveHeight
     */
    private void laserMove(boolean loop, int moveHeight) {
        if (loop) {
            if (mLaserAnim == null) {
                moveHeight = moveHeight == 0 ? ConvertUtils.dp2px(170) : moveHeight;
                mLaserAnim = new TranslateAnimation(0, 0, 0, moveHeight);
                mLaserAnim.setRepeatCount(Animation.INFINITE);
                mLaserAnim.setDuration(1500);
            }
            ivLaser.startAnimation(mLaserAnim);
            ivLaser.setVisibility(VISIBLE);
        } else {
            if (mLaserAnim != null) {
                mLaserAnim.reset();
            }
            ivLaser.clearAnimation();
            ivLaser.setVisibility(GONE);
        }
    }

    @Override
    public void onClick(View v) {
        if (v == flMode) {
            isInput = !isInput;
            if (isInput) {
                pause();
            } else {
                resume();
            }
            Drawable drawable = getResources().getDrawable(isInput ? R.drawable.saoyisao : R.drawable.icon_bar_code);
            if (drawable != null) {
                drawable.setBounds(0, 0, drawable.getMinimumWidth(), drawable.getMinimumHeight());
                tvMode.setCompoundDrawables(drawable, null, null, null);
            }
            tvMode.setText(isInput ? "切换扫码" : "手动输入条形码");
            if (markerView != null) {
                int height = etInput.getHeight() == 0 ? ConvertUtils.dp2px(50) : etInput.getHeight();
                markerView.changeStyle(height, !isInput);
            }
        } else if (v == tvConfirm) {
            //确认,回调到页面中去处理逻辑,清空输入框
            if (mClickListener != null) {
                mClickListener.onClick(CLICK_TAG_CONFIRM, etInput.getText().toString());
            }
            etInput.setText("");
        } else if (v == cbLight) {
            //打开灯光
            CameraManager.get().flashlightUtils();
            tvLightTip.setText(CameraManager.get().isFlashlightOn() ? "点击关闭闪关灯" : "点击开启闪关灯");
        }
    }

    public boolean isInput() {
        return isInput;
    }

    public MarkerView getViewfinderView() {
        return markerView;
    }

    public void drawViewfinder() {
        if (markerView != null) {
            markerView.invalidate();
        }
    }

    //返回值
    public void decodeSucceed(Result result, Bitmap bitmap) {
        if (mBeepManager == null) {
            mBeepManager = new BeepManager(getContext());
            mBeepManager.setMediaResId(R.raw.beep);
        }
        mBeepManager.playBeepSoundAndVibrate();
        if (mListener != null) {
            mListener.decodeSuccess(result, bitmap);
        }
    }

    //获取到返回
    public void getResult(Intent intent) {
        if (mListener != null) {
            mListener.returnIntent(intent);
        }
    }

    public void getMessage(String message) {
        if (mListener != null) {
            mListener.returnMessage(message);
        }
    }

    /**
     * 初始化相机
     */
    private void initCamera(SurfaceHolder surfaceHolder) {
        try {
            CameraManager.get().openDriver(surfaceHolder);
        } catch (IOException ioe) {
            return;
        } catch (RuntimeException e) {
            return;
        }
        if (handler == null) {
            handler = new ScannerViewHandler(this, null, null);
        }
    }

    public void resume() {
        CameraManager.init(BaseYBMApp.getAppContext());
        SurfaceHolder surfaceHolder = surfaceView.getHolder();
        if (hasSurface && !isInput) {
            initCamera(surfaceHolder);
        } else {
            surfaceHolder.addCallback(this);
        }
        if (mBeepManager != null) {
            mBeepManager.updatePrefs();
        }
    }

    public void pause() {
        if (handler != null) {
            handler.quitSynchronously();
            handler = null;
        }
        if (mBeepManager != null) {
            mBeepManager.close();
        }
        if (markerView != null) {
            markerView.recycleLaser();
        }
        CameraManager.get().closeDriver();
    }
}
