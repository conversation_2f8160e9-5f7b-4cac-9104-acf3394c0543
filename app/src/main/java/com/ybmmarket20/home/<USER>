package com.ybmmarket20.home

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentStatePagerAdapter
import com.ybmmarket20.bean.homesteady.RecommendTabInfo
import com.ybmmarket20.common.BaseFragment

/**
 * 首页底部列表适配器
 */
class HomeSteadyLayoutFragmentAdapter(var fm: FragmentManager, val fragments: MutableList<BaseFragment>, val recommendTabInfoList: MutableList<RecommendTabInfo>): FragmentStatePagerAdapter(fm) {
    override fun getCount(): Int = fragments.size

    override fun getItem(position: Int): Fragment = fragments[position]

    override fun getPageTitle(position: Int): CharSequence {
        return recommendTabInfoList[position].name ?: ""
    }
}