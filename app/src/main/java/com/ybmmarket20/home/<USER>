package com.ybmmarket20.home;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.os.Build;
import com.google.android.material.appbar.AppBarLayout;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.core.widget.NestedScrollView;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.ybm.app.bean.NetError;
import com.ybm.app.common.NtpTrustedTime;
import com.ybm.app.view.refresh.RecyclerRefreshLayout;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.AdBagListBean;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.CredentialsDeadlineInfoBean;
import com.ybmmarket20.bean.DialInfoBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.HomeConfigBean;
import com.ybmmarket20.common.AdDialog2;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseFragment;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.ResultListener;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.message.Message;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.StatusBarUtils;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.DialImageView;
import com.ybmmarket20.view.DynamicCommonLayout;
import com.ybmmarket20.view.DynamicHomeLayout;
import com.ybmmarket20.view.DynamicProductTabLayout;
import com.ybmmarket20.view.NoScrollview;
import com.ybmmarket20.view.ShowBottomSheetDialog;

import butterknife.Bind;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**
 * 首页
 */
public class HomeFragment extends BaseFragment {

    @Bind(R.id.appbar)
    AppBarLayout appBarLayout;
    @Bind(R.id.home_header)
    DynamicCommonLayout header;
    @Bind(R.id.home)
    DynamicHomeLayout home;
    @Bind(R.id.home_scrollview)
    NoScrollview homeScrollview;
    @Bind(R.id.rfl_refresh)
    RecyclerRefreshLayout refreshLayout;
    @Bind(R.id.iv_code)
    ImageView ivCode;
    @Bind(R.id.iv_msg)
    ImageView ivMsg;
    @Bind(R.id.title_tv)
    TextView titleTv;
    @Bind(R.id.iv_voice)
    ImageView ivVoice;
    @Bind(R.id.ll_search)
    LinearLayout llSearch;
    @Bind(R.id.iv_fastscroll)
    ImageView fastScroll;
    @Bind(R.id.title_left)
    RelativeLayout msgLayout;
    @Bind(R.id.tv_smg_num)
    TextView tvSmg;
    @Bind(R.id.tv_smg_num_more)
    TextView tvSmgMore;
    @Bind(R.id.iv_ad_suspension)
    ImageView adSuspension;
    @Bind(R.id.tl_product)
    DynamicProductTabLayout tlProduct;
    @Bind(R.id.iv_dial_suspension)
    DialImageView iv_dial_suspension;

    private String searchAction = "ybmpage://searchproduct";
    private BroadcastReceiver br;
    private boolean isShowfastScroll = false;
    protected HomeConfigBean bean;
    private int step = 0;//刷新页面的间隔
    private boolean isRefreshing = false;
    private boolean isHidded = false;
    private int MAXRETRY = 10;
    private int screenHeight;
    private int searchHeight;
    private int diff;
    private boolean open = true;//头是否展开，默认展开的
    private float lastP = -1;
    private boolean isFirst;
    private ShowBottomSheetDialog mDialogLayout;

    private static final int REQUEST_CODE_MSG = 1001;

    @Override
    protected void initData(String content) {
        if (home != null) {
            home.bindHeaderLayout(header);
            home.setApi(AppNetConfig.HOME_DYNAMIC);
        }
        isFirst = true;
        getHomeDialog(1);
        llSearch.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (llSearch != null) {
                    searchHeight = llSearch.getHeight();
                }
            }
        }, 100);
        appBarLayout.addOnOffsetChangedListener(new AppBarLayout.OnOffsetChangedListener() {
            @Override
            public void onOffsetChanged(AppBarLayout appBarLayout, int verticalOffset) {
                if (refreshLayout != null) {
                    refreshLayout.setEnabled(verticalOffset == 0);
                }
                open = (verticalOffset == 0);
                if (open && homeScrollview.getScrollY() > 0) {
                    homeScrollview.smoothScrollTo(0, 0);
                }
                if (searchHeight > 0) {
                    tintView(Math.abs(verticalOffset) * 1.0f / searchHeight * 1.0f);
                }
            }
        });
        screenHeight = UiUtils.getScreenHeight();
        getNewDataFirst();
        initListener();
        getConfig();
        getCoupon();
        getMerchantCredentialsDeadlineInfo();
        getDialInfo();
        br = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (IntentCanst.REFRESH_PAGE.equals(intent.getAction())) {
                    if (home != null) {
                        if (isRefreshing || isHidded || step > MAXRETRY) {
                            return;
                        }
                        isRefreshing = true;
                        home.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                if (home != null && !isRefreshing) {
                                    getNewData(false);
                                    step++;
                                    isRefreshing = false;
                                }
                            }
                        }, step * 500);
                    }
                } else if (IntentCanst.ACTION_SWITCH_USER.equals(intent.getAction())) {
                    //检验密码强度，仅首次
                    checkPwdSafety();
                    getNewData(false);
                    getDialInfo();
                } else if (IntentCanst.ACTION_SWITCH_HOME_INFO.equals(intent.getAction())) {
                    getNewData(false);
                } else if (IntentCanst.ACTION_AD_COLLECT_POP.equals(intent.getAction())) {
                    if (adSuspension == null) {
                        return;
                    }
                    adSuspension.setVisibility(View.VISIBLE);
                } else if (IntentCanst.ACTION_AD_COLLECT_HINT_POP.equals(intent.getAction())) {
                    if (adSuspension == null) {
                        return;
                    }
                    adSuspension.setVisibility(View.GONE);
                }else if (IntentCanst.ACTION_CHANGE_HOME_LAYOUT_TYPE.equals(intent.getAction())) {
                    initMessage();
                }
            }
        };
        IntentFilter intentFilter = new IntentFilter(IntentCanst.MSG_NUM_EDIT);
        intentFilter.addAction(IntentCanst.REFRESH_PAGE);
        intentFilter.addAction(IntentCanst.ACTION_SWITCH_USER);
        intentFilter.addAction(IntentCanst.ACTION_AD_COLLECT_POP);
        intentFilter.addAction(IntentCanst.ACTION_AD_COLLECT_HINT_POP);
        intentFilter.addAction(IntentCanst.ACTION_CHANGE_HOME_LAYOUT_TYPE);
        LocalBroadcastManager.getInstance(getNotNullActivity()).registerReceiver(br, intentFilter);
        NtpTrustedTime.getInstance().check();
        initMessage();

    }

    /**
     * 此方法先暂时不用，用于提示用户密码强度
     */
    private void checkPwdSafety() {
      /*  boolean already_mention = SpUtil.readBoolean("already_mention", false);
        if (!already_mention) {
            RequestParams params = new RequestParams();
            String merchantId = SpUtil.getMerchantid();
            params.put("merchantId", merchantId);
            HttpManager.getInstance().post(AppNetConfig.PASSWORD_VERIFY + "?merchantId=" + merchantId, params, new BaseResponse<Boolean>() {

                @Override
                public void onSuccess(String content, BaseBean<Boolean> obj, Boolean aBoolean) {
                    if (aBoolean != null && !aBoolean) {
                        //密码简单请及时修改
                        final AlertDialogEx alert = new AlertDialogEx(YBMAppLike.getApp().getCurrActivity());
                        alert.setTitle("密码安全提示");//确认下单
                        alert.setMessage("尊敬的药帮忙用户您好，您当前的密码过于简单，存在安全风险，请尽快修改");
                        alert.setCancelButton("取消", null);//取消下单
                        alert.setConfirmButton("去修改", new AlertDialogEx.OnClickListener() {
                            @Override
                            public void onClick(AlertDialogEx dialog, int button) {
                                RoutersUtils.open("ybmpage://alterpassword");
                            }
                        });
                        alert.show();

                        SpUtil.writeBoolean("already_mention", true);
                    }
                }

                public void onFailure(NetError error) {

                }
            });
        }*/
    }

    /*
     * 首页-商户资质临期提醒
     * */
    private void getMerchantCredentialsDeadlineInfo() {

        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.HOME_GETMERCHANTCREDENTIALSDEADLINEINFO).addParam("merchantId", HttpManager.getInstance().getMerchant_id()).build();
        HttpManager.getInstance().post(params, new BaseResponse<CredentialsDeadlineInfoBean>() {

            @Override
            public void onSuccess(String content, BaseBean<CredentialsDeadlineInfoBean> obj, CredentialsDeadlineInfoBean bean) {
                if (titleTv == null) {
                    return;
                }

                if (obj != null && obj.isSuccess()) {

                    if (bean != null) {
                        showSaveDialog(bean.title, bean.credentialsDeadlineRemindMes, new AlertDialogEx.OnClickListener() {
                            @Override
                            public void onClick(AlertDialogEx dialog, int button) {
//                                callPhone(bean.sysUserPhone);
                                RoutersUtils.open("ybmpage://aptitude");
                            }
                        });
                    }
                }
            }
        });

    }

    /*
     * 首页-大转盘
     * */
    private void getDialInfo() {
        if (titleTv == null) {
            return;
        }

        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.HOME_DIAL_INFO).addParam("merchantId", HttpManager.getInstance().getMerchant_id()).build();
        HttpManager.getInstance().post(params, new BaseResponse<DialInfoBean>() {

            @Override
            public void onSuccess(String content, BaseBean<DialInfoBean> obj, DialInfoBean bean) {
                if (titleTv == null) {
                    return;
                }

                if (obj != null && obj.isSuccess()) {

                    if (bean != null) {
                        if (!TextUtils.isEmpty(bean.appImageUrl)) {
                            iv_dial_suspension.setItemData(bean);
                            iv_dial_suspension.setVisibility(View.VISIBLE);
                            //new DialViewHelper(getNotNullActivity(), iv_dial_suspension, homeScrollview);
                        } else {
                            iv_dial_suspension.setVisibility(View.GONE);
                        }
                    }
                }else {
                    iv_dial_suspension.setVisibility(View.GONE);
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                iv_dial_suspension.setVisibility(View.GONE);
            }
        });

    }

    /*
     * 拨打电话
     * */
    private void callPhone(String phone) {

        showBottomSheetDialog(phone);

    }

    /*
     * 拨打电话
     * */
    private void showBottomSheetDialog(String mobile) {
        if (TextUtils.isEmpty(mobile)) {
            ToastUtils.showShort("联系电话为空");
            return;
        }
        mDialogLayout = new ShowBottomSheetDialog(getNotNullActivity());
        mDialogLayout.initData(mobile, -1);
        mDialogLayout.show();
    }

    /*
     * 资质过期提醒对话框
     * */
    public void showSaveDialog(String title, String content, AlertDialogEx.OnClickListener listener) {

        String titleStr = TextUtils.isEmpty(title) ? "资质过期提醒" : title;
        String contentStr = TextUtils.isEmpty(content) ? "您的资质即将过期，为避免影响您的正常采购，请联系工作人员进行新的资质录入" : content;
        AlertDialogEx dialogEx = new AlertDialogEx(getNotNullActivity());
        dialogEx.setTitle(titleStr).setMessage(contentStr).setCancelButton("取消", new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                dialog.dismiss();
            }
        }).setCancelable(false).setConfirmButton("去查看", listener).setCanceledOnTouchOutside(false).show();
    }

    private void initMessage() {
        Message.instance.bindUnreadMsgCount(listener);

    }

    private Message.Listener listener = new Message.Listener() {
        @Override
        public void countChange(int count) {

            Message.showMsgCount(count, tvSmg, tvSmgMore);
        }
    };

    @Override
    protected void initTitle() {

    }

    private void getNewDataFirst() {
        getNewData(true, AppNetConfig.HOME_DYNAMIC_FIRST);
    }


    private void getNewData(final boolean showDialog) {
        getNewData(showDialog, AppNetConfig.HOME_DYNAMIC);
    }

    private void getNewData(final boolean showDialog, String api) {
        if (showDialog) {
            showProgress();
        }
        isRefreshing = true;
        home.getNewData(api, new ResultListener<Integer>() {
            @Override
            public void result(boolean isSuccess, String errorMsg, Integer integer) {
                isRefreshing = false;
                if (showDialog) {
                    dismissProgress();
                }
                if (refreshLayout != null) {
                    refreshLayout.setRefreshing(false);// 关闭刷新动画
                }

                // 顶部
                if (tlProduct != null) {
                    tlProduct.onRefresh();
                }
            }
        });
    }

    //初始化监听
    private void initListener() {

//        homeScrollview.setOnScrollListener(new NoScrollview.OnScrollListener() {
//            @Override
//            public void onScrollChanged(int scrollX, int scrollY, int oldScrollX, int oldScrollY, boolean isUser) {
//
//            }
//        });

        homeScrollview.setOnScrollChangeListener(new NestedScrollView.OnScrollChangeListener() {
            @Override
            public void onScrollChange(NestedScrollView v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {

                if (header != null && header.getVisibility() == View.GONE) {//头部没有东西
                    if (searchHeight > 0) {
                        tintView(Math.abs(scrollY) * 1.0f / searchHeight * 1.0f);
                    }
                }
                diff = oldScrollY - scrollY;
                if (!open && scrollY > 0 && diff > 0 && scrollY > 100 && diff * 1.75f <= scrollY && scrollY <= diff * 2.75f) {//自动展开头部
                    appBarLayout.setExpanded(true, true);
                    open = true;
                }
                showFastScroll(scrollY);

                //判断是否滑到的底部
                if (scrollY == (v.getChildAt(0).getMeasuredHeight() - v.getMeasuredHeight())) {
//                    recyclerView.onLoadMoare();//调用刷新控件对应的加载更多方法

                    if (tlProduct != null) {
                        tlProduct.onLoadMoare();
                    }
                }
            }
        });

        refreshLayout.setOnRefreshListener(new RecyclerRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                if (refreshLayout == null || home == null) {
                    return;
                }
                if (step > MAXRETRY) {
                    step = MAXRETRY / 2;
                }
                getNewData(false);
                getConfig();
                getHomeDialog(2);
                getCancelAttention();
            }

        });

    }

    // 获取是否开启cms
    private void getCancelAttention() {
        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.GET_LAYOUT_TYPE).build();
        // old使用原admin布局，new使用cms配置布局
        HttpManager.getInstance().post(params, new BaseResponse<String>() {
            @Override
            public void onSuccess(String content, BaseBean<String> obj, String s) {
                if ("new".equalsIgnoreCase(s) || "newLayout".equalsIgnoreCase(s)) {
                    //todo 切换到新布局
                    LocalBroadcastManager.getInstance(getContext()).sendBroadcast(new Intent(IntentCanst.ACTION_CHANGE_HOME_LAYOUT_TYPE));
                }
            }
        });

    }


    private void showFastScroll(int y) {
        if (y > screenHeight * 1.2) {
            if (!isShowfastScroll) {
                fastScroll.setVisibility(View.VISIBLE);
                isShowfastScroll = true;
            }
        } else {
            if (isShowfastScroll) {
                fastScroll.setVisibility(View.INVISIBLE);
                isShowfastScroll = false;
            }
        }
    }

    public void scroll2Position(final int dp) {
        if (home != null && homeScrollview != null) {
            homeScrollview.post(new Runnable() {
                @Override
                public void run() {
                    if (home != null && homeScrollview != null) {
                        homeScrollview.smoothScrollTo(0, ConvertUtils.dp2px(dp));
                    }
                }
            });
        }
    }

    @Override
    protected RequestParams getParams() {
        return new RequestParams();
    }

    @Override
    protected String getUrl() {
        return null;
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_home;
    }

    @OnClick({R.id.home_search_rl, R.id.iv_code, R.id.iv_voice, R.id.iv_fastscroll, R.id.title_left, R.id.iv_ad_suspension})
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.home_search_rl:
                RoutersUtils.open(searchAction);
                XyyIoUtil.track(XyyIoUtil.ACTION_HOME_SEARCH);
                break;
            case R.id.iv_fastscroll:
                appBarLayout.setExpanded(true, true);
                homeScrollview.fullScroll(View.FOCUS_UP);
                break;
            case R.id.iv_code:
                XyyIoUtil.track(XyyIoUtil.ACTION_HOME_SCAN);
                RoutersUtils.open("ybmpage://captureactivity");
                break;
            case R.id.iv_voice:
                RoutersUtils.open("ybmpage://searchvoiceactivity");
                break;
            case R.id.title_left://打开消息中心
                XyyIoUtil.track(XyyIoUtil.ACTION_HOME_MESSAGE);
                Message.openMessagePage();
                break;
            case R.id.iv_ad_suspension:
                getCoupon();
                break;

        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (home != null) {
            home.onDestroy();
        }
        if (br != null) {
            LocalBroadcastManager.getInstance(getNotNullActivity()).unregisterReceiver(br);
        }
        ButterKnife.unbind(this);
        Message.instance.releaseListener(listener);

    }

    //获取首页的配置文件
    private void getConfig() {
        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.HOME_CONFIGN).addParam("merchantId", HttpManager.getInstance().getMerchant_id()).build();
        HttpManager.getInstance().post(params, new BaseResponse<HomeConfigBean>() {
            @Override
            public void onSuccess(String content, BaseBean<HomeConfigBean> json, HomeConfigBean bean) {
                ((MainActivity) getNotNullActivity()).setActivity(bean);
                if (titleTv == null) {
                    return;
                }
                HomeFragment.this.bean = bean;
                if (bean != null && !TextUtils.isEmpty(bean.hot_search_key)) {
                    titleTv.setText(bean.hot_search_key);
                    searchAction = "ybmpage://searchproduct?show=" + bean.hot_search_key;
                } else {
                    searchAction = "ybmpage://searchproduct";
                    titleTv.setText("输入关键字搜索商品");
                }
                if (bean == null || TextUtils.isEmpty(bean.kefu_phone) || bean.kefu_phone.length() <= 4) {
                    RoutersUtils.kefuPhone = RoutersUtils.DEFOULT_KEFUPHONE;
                } else {
                    RoutersUtils.kefuPhone = bean.kefu_phone;
                }
                if (bean != null) {
                    setSearchBg(bean.top_search_left_ground, bean.top_search_right_ground);
                }
            }
        });
    }

    //获取首页天降礼包
    private void getCoupon() {
        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.HOME_COUPON).addParam("merchantId", HttpManager.getInstance().getMerchant_id()).build();
        HttpManager.getInstance().post(params, new BaseResponse<AdBagListBean>() {

            @Override
            public void onSuccess(String content, BaseBean<AdBagListBean> obj, AdBagListBean adDataBeans) {
                if (titleTv == null) {
                    return;
                }

                if (obj != null && obj.isSuccess()) {

                    if (adDataBeans != null) {

                        if (adDataBeans.bagList != null && adDataBeans.bagList.size() > 0) {
                            if (adDataBeans.grantType == 2) {

                                AdDialog2.showDialog(adDataBeans);
                            } else {
                                adSuspension.setVisibility(View.VISIBLE);
                                SpUtil.writeInt("show_ad_collect_pop", 0);
                                AdDialog2.showDialog(adDataBeans);
                            }
                        } else {
                            SpUtil.writeInt("show_ad_collect_pop", 1);
                            LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(new Intent(IntentCanst.ACTION_AD_COLLECT_HINT_POP));
                        }
                    }
                }
            }
        });
    }

    //获取首页的弹窗
    private void getHomeDialog(int scene) {// 1 启动 2 下拉刷新 3显示（后台到前台和tab切换,不包含启动）
        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.HOME_DIALOG)
                .addParam("scene", scene + "")
                .addParam("merchantId", HttpManager.getInstance().getMerchant_id())
                .build();
        HttpManager.getInstance().post(params, new BaseResponse<EmptyBean>() {
            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean data) {

            }
        });
    }

    private void setSearchBg(String leftColor, String rightColor) {

        //为了修改主题背景，哎。。。
        int resBg = YBMAppLike.isNewTheme ? R.drawable.base_header_dynamic_bg : R.drawable.base_header_dark_bg;
        Drawable drawable = getResources().getDrawable(resBg);
        if (bean != null && !TextUtils.isEmpty(leftColor) && !TextUtils.isEmpty(rightColor)) {
            int colors[] = new int[2];
            colors[0] = getColor(leftColor);
            colors[1] = getColor(rightColor);
            if (colors != null) {
                drawable = new GradientDrawable(GradientDrawable.Orientation.LEFT_RIGHT, colors);
            }
        }
        if (drawable != null) {
            drawable.setAlpha(0);
        }
        llSearch.setBackground(drawable);
    }

    private void tintView(float p) {
        if (llSearch == null || lastP == p || bean == null) {
            return;
        }
        if (p >= 1) {
            p = 1;
        }
        if (p < 0) {
            p = 0;
        }
        if (p < 0.2 && p > 0) {
            p = 0;
        }
        if (lastP == p) {
            return;
        }
        lastP = p;
        Drawable drawable = llSearch.getBackground();
        if (drawable != null) {
            drawable.setAlpha((int) (p * 255));
        }

        if (p >= 0.8) {

            StatusBarUtils.setDarkMode(getNotNullActivity());
            ivCode.setImageDrawable(UiUtils.getDrawable(getNotNullActivity(), R.drawable.icon_nav_scan03));
            ivMsg.setImageDrawable(UiUtils.getDrawable(getNotNullActivity(), R.drawable.msg_icon));
        } else {

            StatusBarUtils.setLightMode(getNotNullActivity());
            if (bean != null && bean.checkIcon == 1) {
                ivCode.setImageDrawable(UiUtils.getDrawable(getNotNullActivity(), R.drawable.icon_nav_scan03));
                ivMsg.setImageDrawable(UiUtils.getDrawable(getNotNullActivity(), R.drawable.msg_icon));
            } else {
                ivCode.setImageDrawable(UiUtils.getDrawable(getNotNullActivity(), R.drawable.icon_nav_scan));
                ivMsg.setImageDrawable(UiUtils.getDrawable(getNotNullActivity(), R.drawable.icon_nav_scan02));
            }

        }

    }

    protected int getColor(String color) {
        try {
            return Color.parseColor(color);
        } catch (Exception e) {
            return 0;
        }
    }


    private String getImageUrl(String img) {
        if (TextUtils.isEmpty(img)) {
            return img;
        }
        if (img.startsWith("http") || img.startsWith("Http")) {
            return img;
        } else {
            return AppNetConfig.getCDNHost() + img;
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        isHidded = true;
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        isHidded = hidden;
        if (hidden) {
            if (home != null) {
                home.onPause();
            }
        } else {
            if (lastP >= 0.8) {
                StatusBarUtils.setDarkMode(getNotNullActivity());
            } else {
                StatusBarUtils.setLightMode(getNotNullActivity());
            }

            if (home != null) {
                home.onResume();
            }
            if (isFirst) {
                isFirst = false;
            } else {
                getHomeDialog(3);
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (isFirst) {
            isFirst = false;
        } else {
            if (MainActivity.getMainActivity() != null && MainActivity.getMainActivity().position == 0) {
                getHomeDialog(3);
            }
        }

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

}
