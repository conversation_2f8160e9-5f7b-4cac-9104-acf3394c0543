package com.ybmmarket20.common

import android.content.Context
import com.analysys.AnalysysAgent
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.reportBean.JGPageListCommonBean
import com.ydmmarket.report.manager.TrackThreadPoolManager
import kotlinx.coroutines.delay
import java.io.Serializable
import java.util.concurrent.Callable
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import java.util.concurrent.TimeoutException

/**
 *  极光埋点 埋点管理类
 *  目前项目中存在两种埋点： 极光埋点和老的埋点方式
 *
 */
interface JGTrackManager {

	companion object {
		/**
		 * 事件埋点
		 * @param eventName String
		 * @param contentMap HashMap<String, out Any>
		 */
		@Deprecated("现在改用ReportManager.getInstance().report()的方式去埋点")
		fun eventTrack(
				mContext: Context,
				eventName: String,
				contentMap: HashMap<String, out  Any> = hashMapOf()
		) {

			AnalysysAgent.track(mContext, eventName, contentMap)
		}

		/**
		 * 手动添加pageView事件
		 * @param pageName String 页面名称title
		 * @param properties HashMap<String, out Any> 属性
		 */
		fun pageViewTrack(
				mContext: Context,
				pageName: String,
				properties: HashMap<String, out Any> = hashMapOf()
		) {

			AnalysysAgent.pageView(mContext, pageName, properties)
		}

		//设置通用属性
		fun setSuperProperties(mContext: Context, contentMap: HashMap<String, out Any>){

			AnalysysAgent.registerSuperProperties(mContext,contentMap)
		}

		/**
		 * 获取单个通用属性
		 * @param mContext Context
		 * @param key String
		 */
		fun getSuperProperty(mContext: Context,key:String):Any?{
			return AnalysysAgent.getSuperProperty(mContext,key)
		}

		/**
		 * 获取所有通用属性
		 * @param mContext Context
		 * @param key String
		 * @return Map<String,Any>?
		 */
		fun getSuperProperties(mContext: Context,key:String):Map<String,Any>?{
			return AnalysysAgent.getSuperProperties(mContext)
		}

		//设置单个通用属性
		fun setSuperProperty(mContext: Context, superPropertyName:String,
		                       superPropertyValue:Any){

			AnalysysAgent.registerSuperProperty(mContext,superPropertyName,superPropertyValue)
		}

		fun unRegisterSuperProperty(mContext: Context, superPropertyName:String){
			AnalysysAgent.unRegisterSuperProperty(mContext, superPropertyName);
		}

		//立即上传埋点数据
		fun flush(mContext:Context){
			AnalysysAgent.flush(mContext)
		}

		/**
		 * 匿名ID与用户关联
		 * @param mContext Context
		 * @param aliasId String
		 */
		fun alias(mContext: Context, aliasId: String) {
			AnalysysAgent.alias(
					mContext,
					aliasId);
		}

		fun getDistinctId(): String {
			return timeoutGetDistinctId(YBMAppLike.getAppContext())?:""
		}

		/**
		 * 请求体添加distinctid，偶现存在阻塞情况，超时处理
		 */
		private fun timeoutGetDistinctId(mContext: Context): String? {
//			val task = Callable {
//				AnalysysAgent.getDistinctId(mContext)
//			}
//			val future = TrackThreadPoolManager.submit(task)
//			var distinctId: String? = ""
//			try {
//				distinctId = future[100, TimeUnit.MILLISECONDS]
//			} catch (e: TimeoutException) {
//				println("Timeout occurred while getting DISTINCTID.")
//			} catch (e: Exception) {
//				e.printStackTrace()
//			}
//			return distinctId
			return ""
		}
	}

	object GlobalVariable {

		var mJgOperationInfo: JgOperationPositionInfo? = null

		//搜索页的搜出来的 需要全局携带的商品信息
		var mJgSearchRowsBean: RowsBean? = null

		//搜索页的搜出来的 需要全局携带的商品信息其他参数
		var mJgSearchSomeField: JgSearchSomeField? = null
	}

	object Common {
		/**
		 * 通用属性
		 */

		const val FIELD_CHANNEL = "\$channel" //下载渠道
		const val FIELD_PLATFORM = "\$platform" //平台
		const val FIELD_UP_DATE = "up_date" //上报日期
		const val FIELD_ACCOUNT_ID = "account_id" //账号ID
		const val FIELD_MERCHANT_ID = "merchant_id" //所属公司/店铺/医院ID
		const val FIELD_MERCHANT_NAME = "merchant_name" //所属公司/店铺/医院名称
		const val FIELD_PROJECT_NAME = "project_name" //项目名称
		const val FIELD_PROVINCE = "province" //省份
		const val FIELD_BUSINESSTYPE = "businessType" //店铺类型
		const val FIELD_BUSINESSTYPENAME = "businessTypeName" //店铺类型名称
		const val FIELD_H5_SESSION_ID = "sessionId" //给H5的字段
		const val FIELD_H5_JGSPID = "jgSpid" //给H5的jgspid

		const val EVENT_ADD_TO_CART = "add_to_cart" //加入购物车
		const val EVENT_RESOURCE_CLICK = "resource_click" //全平台商品点击
		const val EVENT_COUPONS_GET = "coupons_get" //获取优惠券


		const val MODULE_OPERATIONS = "搜索运营位"
		const val MODULE_SEARCH_FEED = "搜索Feed流"
		const val MODULE_PRODUCT_LIST = "商品列表"

	}

	object FIELD{
		const val FIELD_PAGE_ID = "page_id" //页面ID
		const val FIELD_TITLE = "\$title" //标题
		const val FIELD_URL = "\$url" //页面URL(含参)
		const val FIELD_URL_DOMAIN = "\$url_domain" //页面URL（去参）
		const val FIELD_REFERRER = "\$referrer" //页面来源
		const val FIELD_REFERRER2 = "referrer"
		const val FIELD_REFERRER_TITLE = "referrer_title" //页面來源标题
		const val FIELD_MODULE = "module" //所属模块
		const val FIELD_RESOURCE_ID = "resource_id" //资源位ID
		const val FIELD_RESOURCE_NAME = "resource_name" //资源位名称
		const val FIELD_RESOURCE_TYPE = "resource_type" //资源位类型   商品卡片、内容卡片
		const val FIELD_RESOURCE_CONTENT_TYPE = "resource_content_type" //资源位内容类型  商品、内容、活动、店铺
		const val FIELD_ACTIVITY_ID = "activity_id" //活动ID
		const val FIELD_ACTIVITY_NAME = "activity_name" //活动名称
		const val FIELD_RANK = "rank" //资源位排序
		const val FIELD_NAVIGATION_1 = "navigation_1" //一级导航
		const val FIELD_NAVIGATION_2 = "navigation_2" //二级导航
		const val FIELD_PRODUCT_ID = "product_id" //商品ID
		const val FIELD_PRODUCT_NAME = "product_name" //商品名称
		const val FIELD_PRODUCT_TYPE = "product_type" //商品类型
		const val FIELD_PRODUCT_ORIGINAL_PRICE = "product_original_price" //商品原价
		const val FIELD_PRODUCT_PRESENT_PRICE = "product_present_price" //商品现价
		const val FIELD_ACTIVITY_TYPE = "activity_type" //促销活动类型 满减/折扣/拼团..
		const val FIELD_PRODUCT_FIRST = "product_first" //商品一级分类
		const val FIELD_PRODUCT_LABEL = "product_label" //商品标签   热卖商品、热搜商品...
		const val FIELD_SHOP_ID = "shop_id" //商品店铺ID
		const val FIELD_SHOP_NAME = "shop_name" //商品店铺名称
		const val FIELD_KEY_WORD = "key_word" //搜索关键词
		const val FIELD_SEARCH_TYPE = "search_type" //搜索类型  历史搜索、热词搜索、提示词
		const val FIELD_SUG_RANK = "sug_rank" //提示词排序
		const val FIELD_LOCATION_URL = "location_url" //资源位点击跳转URL
		const val FIELD_RESULT_CNT = "result_cnt" //结果数量
		const val FIELD_REFERRER_MODULE = "referrer_module" //来源模块 //首页：信息流、营销位、banner、商品详情页：热销精选、大家都在团、活动页：{活动页入口}-活动页-活动页标题
		const val FIELD_DURATION = "duration" //时长
		const val FIELD_PROMO_DESC = "promo_desc" //促销描述
		const val FIELD_IS_SUCCESS = "is_success" //是否成功
		const val FIELD_REASON = "reason" //失败原因
		const val FIELD_ENTRANCE = "entrance" //来源
		const val FIELD_ACTIVITY_ENTRANCE = "activity_entrance" //H5来源
		const val FIELD_BTN_NAME = "btn_name" //按钮名称
		const val FIELD_BTN_DESC = "btn_desc" //按钮描述
		const val FIELD_SPU_CODING = "SPU_coding" //按钮描述
		const val FIELD_COMMON_NAME = "common_name" //按钮描述
		const val FIELD_SUBMIT_ORDER_TIME = "submitordertime" //用户点击提交按钮时的时间戳
		const val FIELD_ORDER_AMOUNT = "order_amount" //订单金额
		const val FIELD_PRODUCT_NUMBER = "product_number" //商品数量
		const val FIELD_OPERATE_TYPE = "operate_type"
		const val FIELD_SEARCH_SORT_STRATEGY_ID = "search_sort_strategy_id" //搜索的a/b策略id
		const val FIELD_ADDCARTTIME = "addcarttime" //加购时间
		const val FIELD_SHARE_PAGE = "share_page" //分享所在页面
		const val FIELD_SHARE_TYPE = "share_type" //分享方式
		const val FIELD_COUPON_TYPE = "coupon_type" //优惠券类型
		const val FIELD_COUPON_PRICE = "coupon_price" //优惠券优惠金额
		const val FIELD_USE_START_TIME = "use_start_time" //卡券有效使用开始时间
		const val FIELD_USE_END_TIME = "use_end_time" //卡券有效使用结束时间
		const val FIELD_PAIXU = "paixu" //排序
		const val FIELD_GUIGE = "guige" //规格
		const val FIELD_USE_CHANGJIA = "changjia" //厂家
		const val FIELD_SHANGJIA = "shangjia" //商家
		const val FIELD_FENLEI = "fenlei" //商家
		const val FIELD_FUWU = "fuwu" //服务
		const val FIELD_MINPRICE = "minprice" //最低价格
		const val FIELD_MAXPRICE = "maxprice" //最高价格
		const val FIELD_YPLX = "yplx" //药品类型
		const val FIELD_KEYONGQUAN = "keyongquan" //可用券
		const val FIELD_JINGSHUN = "jingshun" //京东/顺丰
		const val FIELD_YOUXUAN = "youxuan" //优选
		const val FIELD_ZIYING = "ziying" //自营
		const val FIELD_ONLYZHONGYAO = "onlyzhongyao" //只看中药
		const val FIELD_PINTUANBAOYOU = "pintuanbaoyou" //拼团包邮
		const val FIELD_TONGSHENG = "tongsheng" //同省
		const val FIELD_OPERATION_ID = "operation_id" //运营位id
		const val FIELD_OPERATION_RANK = "operation_rank" //运营位Rank
		const val FIELD_LIST_POSITION_TYPE = "list_position_type" //坑位类型
		const val FIELD_LIST_POSITION_TYPENAME = "list_position_typename" //坑位类型名称
		const val DYNAMIC_FILTER_TYPE = "tag_filter_type" //动态筛选标签类
		const val DYNAMIC_FILTER = "tag_filter" //动态筛选标签名
		const val DYNAMIC_FILTER_PERIOD_VALIDITY = "period_validity" //有效期
	}

	/**
	 * 首页
	 */
	object TrackHomePage{
		const val TITLE = "首页"
		const val PAGE_ID = "shouye"
		const val TRACK_HOME_OUT_URL = "com.ybmmarket20.home.newpage.HomeSteadyLayoutFragmentV3"
		const val TRACK_HOME_INNER_URL = "com.ybmmarket20.home.newpage.HomeTabCommonFragment"

		const val EVENT_BTN_CLICK = "btn_click" //按钮点击
		const val EVENT_RESOURCE_CLICK = "resource_click" //资源位点击
	}

	//搜索页 不再区分新老 改动最小就把这里的文案改了  ===============start
	object TrackOldSearchResult{
		const val TITLE = "搜索结果页"
		const val PAGE_ID = "sousuojieguoye"
		const val EVENT_SEARCH = "search" //搜索
	}

	object TrackOldSearchIntermediateState{
		const val TITLE = "搜索中间页"
		const val PAGE_ID = "sousuozhongjianye"
	}
	//搜索结果页
	object TrackSearchResult {

		const val TITLE = "搜索结果页"
		const val PAGE_ID = "sousuojieguoye"
		const val TRACK_URL = "com.ybmmarket20.search.SearchProductOPActivity"

		const val EVENT_RESOURCE_CLICK = "resource_click" //资源位点击

		const val EVENT_SEARCH = "search" //搜索
		const val EVENT_FILTER = "filter" //在搜索页面选择筛选条件且触发筛选后上报
		const val EVENT_BTN_CLICK = "btn_click" //按钮点击
		const val EVENT_DYNAMIC_LABEL_EXPOSURE = "page_search_dynamic_filter_exposure"//动态标签曝光
		const val EVENT_DYNAMIC_LABEL_CLICK = "page_search_dynamic_filter_click"//动态标签曝光

	}

	//搜索中间页
	object TrackSearchIntermediateState {

		const val TITLE = "搜索中间页"
		const val PAGE_ID = "sousuozhongjianye"
		const val URL = "com.ybmmarket20.search.SearchProductOPActivity"

		const val SEARCH_MODULE_HISTORY = "历史搜索"
		const val SEARCH_MODULE_FIND = "搜索发现"
		const val SEARCH_MODULE_SELECTED = "精选搜索"
		const val SEARCH_MODULE_HOT = "热卖排行榜"
		const val SEARCH_MODULE_SUG = "sug"

		const val SEARCH_BTN_SCAN = "扫一扫"
		const val SEARCH_BTN_VOICE = "语音"
		const val SEARCH_BTN_HISTORY = "历史搜索"
		const val SEARCH_BTN_CLEAN = "清空"
		const val SEARCH_BTN_MORE = "更多"
		const val SEARCH_BOX = "搜索框"


		const val EVENT_BTN_CLICK = "btn_click" //按钮点击

		const val EVENT_RESOURCE_CLICK = "resource_click" //搜索词点击触发、sug点击时触发、筛选条件点击时触发
	}

	//新搜索页================end

	object TrackShopSearch{
		const val TITLE = "店铺内搜索页"
		const val PAGE_ID = "dianpuneisousuoye"
	}

	//商品详情页
	object TrackProductDetail {

		const val TITLE = "商品详情页"
		const val PAGE_ID = "productDetail"
		const val URL = "com.ybmmarket20.activity.ProductDetailActivity"

		const val EVENT_BTN_CLICK = "btn_click" //按钮点击
		const val EVENT_PRODUCT_VIEW = "product_view" //浏览商品详情页
		const val EVENT_PRODUCT_VIEW_CLOSE = "product_view_close" //退出商品详情页
		const val EVENT_BTN_EXPOSURE = "btn_exposure"; // 商品按钮曝光

	}


	//购物车
	object TrackShoppingCart{

		const val TITLE = "购物车"
		const val PAGE_ID = "gouwuche"

		const val TRACK_URL = "com.ybmmarket20.home.CartFragmentV3"

		const val EVENT_BTN_CLICK = "btn_click" // 【领券结算】点击
	}

	//提交订单页
	object TrackOrderSubmitDetail{

		const val TITLE = "提交订单页"
		const val PAGE_ID = "tijiaodingdanye"


		const val EVENT_BTN_CLICK = "btn_click" // 【提交订单】点击
//		const val EVENT_ORDER_SUBMIT = "order_submit" // 提交订单
//		const val EVENT_ORDER_SUBMIT_DETAIL = "order_submit_detail" // 提交订单商品明细
	}

	/**
	 * 首页常购清单模块
	 */
	object TrackFrequentPurchase{
		const val TITLE = "常购清单"
		const val PAGE_ID = "changgouqingdan"
	}

	/**
	 * 店铺列表
	 */
	object TrackShopList{

		const val TITLE = "店铺列表"
		const val PAGE_ID = "dianpuliebiao"

		const val EVENT_RESOURCE_CLICK = "resource_click" //资源位点击
	}

	/**
	 * 店铺主页
	 */
	object TrackShopMain{
		const val TITLE = "店铺主页"
		const val PAGE_ID = "dianpuzhuye"

		const val EVENT_SHOP_VIEW = "shop_view" //进入店铺
	}

	/**
	 * 店铺同款
	 */
	object TrackShopSameGoods{
		const val TITLE = "店铺同款"
		const val PAGE_ID = "dianputongkuan"
	}

	/**
	 * 全部药品
	 */
	object TrackAllDrugs{
		const val TITLE = "全部药品"
		const val PAGE_ID = "quanbuyaopin"
		const val TRACK_URL = "com.ybmmarket20.home.newpage.AllDrugActivity"
	}

	/**
	 * 我的
	 */
	object TrackMine{
		const val TITLE = "我的"
		const val PAGE_ID = "wode"
		const val TRACK_URL = "com.ybmmarket20.home.mine.MineFragment2"

		const val EVENT_BTN_CLICK="btn_click" //我的页面-按钮点击

	}

	/**
	 * 设置
	 */
	object TrackSetting{
		const val TITLE = "设置"
		const val PAGE_ID = "shezhi"

		const val EVENT_BTN_CLICK="btn_click" //按钮点击
	}
	object TrackMineWealth{
		const val TITLE = "我的财富"
		const val PAGE_ID = "wodecaifu"

		const val EVENT_BTN_CLICK="btn_click" //按钮点击
	}

	/**
	 * 我的-常购清单
	 */
	object TrackMineFrequentPurchase{
		const val TITLE = "我的常购清单"
		const val PAGE_ID = "wodechanggouqingdan"
	}

	/**
	 * 我的收藏
	 */
	object TrackMineCollect {
		const val TITLE = "我的收藏"
		const val PAGE_ID = "wodeshoucang"
		const val URL = "com.ybmmarketkotlin.feature.collect.CollectActivity"
		const val EVENT_BTN_CLICK = "btn_click"
	}

	/**
	 * 注册页面
	 */
	object TrackRegister{
		const val TITLE = "注册页"
		const val PAGE_ID = "register"
		const val URL = "com.ybmmarket20.activity.RegisterV2Activity"

		const val EVENT_BTN_CLICK = "btn_click" //按钮点击
	}

	/**
	 * 登录页面
	 */
	object TrackLogin {
		const val TITLE = "登录页"
		const val PAGE_ID = "login"
		const val URL = "com.ybmmarket20.activity.LoginActivity"
	}

	/**
	 * 我的-地址管理
	 */
	object TrackAddressList {
		const val TITLE = "我的-地址管理"
		const val PAGE_ID = "wodedizhiguanli"
		const val URL = "com.ybmmarket20.activity.AddressListActivity"
		const val EVENT_BTN_CLICK = "btn_click"
	}

	/**
	 * 我的-资质认证
	 */
	object TrackAptitude {
		const val TITLE = "我的-资质认证"
		const val PAGE_ID = "zizhirenzheng"
		const val URL = "com.ybmmarket20.activity.AptitudeActivity"
		const val EVENT_BTN_CLICK = "btn_click"
		const val MODULE = "功能"
	}

	/**
	 * 我的-订单页
	 */
	object TrackOrderList {
		const val TITLE = "我的-订单页"
		const val PAGE_ID = "wodedingdan"
		const val URL = "com.ybmmarket20.business.order.ui.OrderListActivity"
		const val EVENT_BTN_CLICK = "btn_click"
	}

	/**
	 * 我的-常够清单
	 */
	object TrackOftenBuy {
		const val TITLE = "常购清单"
		const val PAGE_ID = "changgouqingdan"
		const val URL = "com.ybmmarketkotlin.activity.OftenBuyActivity"
		const val EVENT_BTN_CLICK = "btn_click"
	}

	/**
	 * 资质/配送
	 */
	object TrackQualificationAndAfterSale {
		const val TITLE = "资质/配送"
		const val PAGE_ID = "shangjiazizhiye"
		const val URL = "com.ybmmarket20.activity.QualificationAndAfterSaleActivity"
		const val EVENT_BTN_CLICK = "btn_click"
	}

	/**
	 * 优惠券弹窗
	 */
	object TrackCartCoupon {
		const val TITLE = "优惠券弹出页"
		const val PAGE_ID = "youhuiquantanchuye"
		const val EVENT_BTN_CLICK = "btn_click"
	}

	/**
	 * 语音搜素
	 */
	object TrackSearchVoice {
		const val TITLE = "语音搜索"
		const val PAGE_ID = "yuyinsousuo"
	}

	/**
	 * 扫一扫
	 */
	object TrackCapture {
		const val TITLE = "扫一扫"
		const val PAGE_ID = "saoyisao"
	}

	/**
	 * H5页面
	 */
	object TrackCommonH5 {
		const val URL = "com.ybmmarket20.activity.CommonH5Activity"
		const val TITLE = "活动页面标题"
		const val PAGE_ID = "activity"
		const val EVENT_BTN_CLICK = "btn_click"
	}

	/**
	 * 随心拼
	 */
	object TrackSpellGroupRecommendSelectedGoods {
		const val TITLE = "随心拼"
		const val PAGE_ID = "suixinpai"
		const val EVENT_BTN_CLICK = "btn_click"
		const val URL = "com.ybmmarket20.activity.SpellGroupRecommendSelectedGoodsActivity"
	}

	/**
	 * 我的收藏
	 */
	object TrackMineSupplier {
		const val TITLE = "我的-供应商"
		const val PAGE_ID = "wodegongyingshang"
		const val EVENT_BTN_CLICK="btn_click" //按钮点击
	}

	/**
	 * 我的优惠券
	 */
	object TrackMineCoupon {
		const val TITLE = "我的优惠券"
		const val PAGE_ID = "wdyhq"

		const val EVENT_BTN_CLICK = "btn_click" //按钮点击
	}

	object TrackShopCarFindSameGoods {
		const val TITLE = "购物车-失效品找相似"
		const val PAGE_ID = "gouwucheshixiaoxiangsipin"
	}

}

/**
 *  传递极光埋点的参数Bean
 * @constructor
 */
data class JgTrackBean(
		var jgReferrer: String? = "", //页面来源
		var jgReferrerTitle: String? = "", //页面来源标题,
		var jgReferrerModule: String? = "", //来源模块
		var module: String? = "", //所属模块
		var pageId: String? = "",
		var title: String? = "",
		var entrance: String? = "", //来源全路径
		var activityEntrance: String? = "", // h5带过来的来源
		var url: String? = "", //		var operationId: String = "", //运营位Id
		var operationRank:Int? = null, //运营位下标  运营位的信息最好取下面对象里面的字段 这个字段是给下面对象过渡传递用的
		var mJgOperationPositionInfo: JgOperationPositionInfo? = null, //运营位信息
		//		var operationRank: String = "", //运营位下标
		//		var rank:Int = 1, //商品下标
		var rank:Int? = 1, // 商品下标
		var productId: String?="",
		var productType:String?="",
		var navigation_1:String?="",
		var navigation_2:String?="") : Serializable {

	constructor(
			jgReferrer: String? = "", //页面来源
			jgReferrerTitle: String? = "", //页面来源标题,
			jgReferrerModule: String? = "", //来源模块
			module: String? = "", //所属模块
			pageId: String? = "",
			title: String? = "",
			entrance: String? = "", //来源全路径
			activityEntrance: String? = "", // h5带过来的来源
			   ) : this(
			jgReferrer,
			jgReferrerTitle,
			jgReferrerModule,
			module,
			pageId,
			title,
			entrance,
			activityEntrance,
			"",
			0,
			null)

	constructor(
			jgReferrer: String? = "", //页面来源
			jgReferrerTitle: String? = "", //页面来源标题,
			jgReferrerModule: String? = "", //来源模块
			module: String? = "", //所属模块
			pageId: String? = "",
			title: String? = "",
			entrance: String? = "", //来源全路径
			activityEntrance: String? = "", // h5带过来的来源
			url: String? = "") : this(
			jgReferrer,
			jgReferrerTitle,
			jgReferrerModule,
			module,
			pageId,
			title,
			entrance,
			activityEntrance,
			url,
			0,
			null)

}


/**
 * 极光埋点全局携带的运营位商品信息
 * @property productId String  商品Id
 * @property operationId String 运营位Id
 * @property rank Int 下标 从1开始
 * @property operationRank Int 运营位在列表中的下标 从1开始
 * @constructor
 */
data class JgOperationPositionInfo(
		val productId: String?, val operationId: String? = null, val rank: Int?, val operationRank: Int? = null)

data class JgSearchSomeField(
		var mJgPageListCommonBean: JGPageListCommonBean?=null,
		var rank:Int? = 0
)
