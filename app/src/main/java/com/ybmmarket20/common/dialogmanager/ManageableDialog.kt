package com.ybmmarket20.common.dialogmanager

/**
 * @class   ManageableDialog
 * <AUTHOR>
 * @date  2024/4/19
 * @description   优先级弹窗接口
 */
interface ManageableDialog {

	fun show()

	fun dismiss(isPushBack: Boolean)

	fun setOnDismissListener(listener:OnDismissListener)

	fun setOnShowListener(listener: OnShowListener)

	/**
	 * 是否满足show的条件，无条件则为true  可根据实际条件更改true,false
	 */
	fun isCanShow():Boolean
}

fun interface OnShowListener{
	fun onShow()
}

fun interface OnDismissListener{

	/**
	 * @param isPushBack Boolean
	 */
	fun onDismiss(isPushBack: Boolean)
}