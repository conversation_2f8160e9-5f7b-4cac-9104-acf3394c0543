package com.ybmmarket20.common

import android.content.Context
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.GestureDetector.SimpleOnGestureListener
import android.view.MotionEvent
import androidx.recyclerview.widget.RecyclerView
import kotlin.math.abs


/**
 * 禁止手滑的RecycleView,可以触发Item的点击事件
 */
class NoHandSlidingRecycleView(val mContext: Context, attrs: AttributeSet) : RecyclerView(mContext, attrs) {


    override fun onTouchEvent(event: MotionEvent): Boolean {
        var startX = 0f
        var startY = 0f
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                startX = event.x
                startY = event.y
            }

            MotionEvent.ACTION_MOVE -> {
                val dy = abs(event.y - startY)
                if (dy > 0) {
                    return true // 拦截垂直方向的滑动事件
                }
            }
        }
        return false
    }

}