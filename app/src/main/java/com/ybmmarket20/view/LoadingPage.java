package com.ybmmarket20.view;

import android.content.Context;
import android.content.Intent;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.ybm.app.bean.NetError;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.UiUtils;

/**
 * Created by asus on 2016/3/11.
 */
public abstract class LoadingPage extends FrameLayout {

    private Context mContext;

    private LayoutParams lp;

    public LoadingPage(Context context) {
        this(context, null);
    }

    public LoadingPage(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public LoadingPage(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mContext = context;
        init();
    }

    private View errorView;

    private View successView;

    public static final int LOADING_PAGE_STATE = 1;

    public static final int LOADING_ERROR_STATE = 2;

    public static final int LOADING_EMPTY_STATE = 3;

    public static final int LOADING_SUCCESS_STATE = 4;

    //当前默认状态
    public static int CURRENT_STATE = 1;

    private void init() {
        lp = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        if (errorView == null) {
            errorView = UiUtils.getXmlView(R.layout.page_loading);
            errorView.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    show();
                }
            });
            addView(errorView, lp);
        }
        showSafePage();
    }

    private void showSafePage() {
        UiUtils.runOnMainThread(new Runnable() {
            @Override
            public void run() {
                showPage();
            }
        });

    }

    private void showPage() {
        if (CURRENT_STATE == LOADING_PAGE_STATE) {
            ((BaseActivity) mContext).showProgress();
        } else {
            ((BaseActivity) mContext).dismissProgress();
        }
        errorView.setVisibility(CURRENT_STATE == LOADING_ERROR_STATE ? View.VISIBLE : View.GONE);
        if (successView == null) {
            successView = View.inflate(mContext, layoutId(), null);
            addView(successView, lp);
        }
        successView.setVisibility(CURRENT_STATE == LOADING_SUCCESS_STATE ? View.VISIBLE : View.GONE);
    }

    private ResultState resultState = null;

    /**
     * 提供外部请求网络的方法
     * 根据网络请求的结果--->当前界面的一个加载状态--->展示界面 [如果展示的是成功界面，需要进行一系列的处理]
     */
    public void show() {
        if (CURRENT_STATE != LOADING_PAGE_STATE) {
            CURRENT_STATE = LOADING_PAGE_STATE;
        }
        showSafePage();
        String url = url();
        if (TextUtils.isEmpty(url) || params() == null) {
            resultState = ResultState.SUCCESS;
            resultState.setContent("");
            loadPage();
        } else {
            HttpManager.getInstance().post(url, params(), new BaseResponse<EmptyBean>() {

                @Override
                public void onSuccess(String content,BaseBean<EmptyBean> data, EmptyBean obj) {
                    if (TextUtils.isEmpty(content)) {
                        resultState = ResultState.EMPTY;
                        resultState.setContent("");
                    } else {
                        resultState = ResultState.SUCCESS;
                        resultState.setContent(content);
                    }
                    loadPage();
                    //更新购物车数量广播
                    LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(new Intent(IntentCanst.ACTION_SHOPNUMBER));
                }

                @Override
                public void onFailure(NetError error) {
                    resultState = ResultState.ERROR;
                    resultState.setContent("");
                    loadPage();
                }
            });
        }
    }

    private void loadPage() {
        CURRENT_STATE = resultState.getState();
        showSafePage();
        if (CURRENT_STATE == LOADING_SUCCESS_STATE) {
            onSuccess(resultState, successView);
        }
    }

    protected abstract void onSuccess(ResultState resultState, View successView);

    protected abstract RequestParams params();

    protected abstract String url();

    public abstract int layoutId();

    public enum ResultState {

        ERROR(2), EMPTY(3), SUCCESS(4);

        private int state;

        private String content;

        ResultState(int state) {
            this.state = state;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public int getState() {
            return state;
        }
    }
}
