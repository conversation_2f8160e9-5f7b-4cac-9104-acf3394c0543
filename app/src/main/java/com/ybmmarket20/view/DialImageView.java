package com.ybmmarket20.view;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.DialInfoBean;
import com.ybmmarket20.common.JGTrackManager;
import com.ybmmarket20.common.JGTrackTopLevelKt;
import com.ybmmarket20.common.TrackManager;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.homesteady.callback.IHomeAlertAnalysisCallback;
import com.ybmmarket20.view.homesteady.callback.IHomeAlertAnalysisCallbackV3;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;

public class DialImageView extends androidx.appcompat.widget.AppCompatImageView {

    private IHomeAlertAnalysisCallback mAnalysisCallback;
    private IHomeAlertAnalysisCallbackV3 mBigWheelAnalysisCallback;
    private String imgHeader = AppNetConfig.CDN_HOST;
    protected ItemClick itemClick = new ItemClick();
    public int homeAlertType = 0;
    private Context mContext;
    private DialInfoBean items;

    public static final int DIAL_REQUEST_CODE = 10010;

    public DialImageView(Context context) {
        this(context, null);
    }

    public DialImageView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public DialImageView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        mContext = context;
    }

    public void setItemData(DialInfoBean items) {
        setItemData(items, 0);
    }

    public void setItemData(DialInfoBean items, int homeAlertType) {
        this.homeAlertType = homeAlertType;
        this.items = items;
        try {
            if (mAnalysisCallback != null) {
                mAnalysisCallback.onTurnTableExposureCallback();
            }
            if (mBigWheelAnalysisCallback != null) {
                mBigWheelAnalysisCallback.onAlertExposureCallback(items.trackData);
            }
            setImageView(this, items);
            setTag(R.id.tag_action, items.appJumpUrl);
            setOnClickListener(itemClick);
        } catch (IndexOutOfBoundsException e) {
            return;
        }
    }

    public void setHomeAlertAnalysisCallback(IHomeAlertAnalysisCallback analysisCallback) {
        mAnalysisCallback = analysisCallback;
    }

    public void setIHomeBigWheelAnalysisCallback(IHomeAlertAnalysisCallbackV3 bigWheelAnalysisCallback) {
        mBigWheelAnalysisCallback = bigWheelAnalysisCallback;
    }

    private void setImageView(ImageView view, DialInfoBean bean) {
        try {
            if (TextUtils.isEmpty(bean.appImageUrl)) {
                return;
            } else {
                ImageHelper.with(getContext()).load(getImgUrl(bean.appImageUrl)).dontAnimate().dontTransform().diskCacheStrategy(DiskCacheStrategy.SOURCE).into(view);
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    private String getImgUrl(String url) {
        if (TextUtils.isEmpty(url)) {
            return "";
        }
        if (url.startsWith("http") || url.startsWith("Http")) {
            return url;
        } else {
            return imgHeader + url;
        }
    }

    //通用点击跳转
    public class ItemClick implements OnClickListener {
        @Override
        public void onClick(final View v) {
            v.setEnabled(false);
            String action = (String) v.getTag(R.id.tag_action);
            click(action);
            v.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (v != null) {
                        v.setEnabled(true);
                    }
                }
            }, 500);
        }
    }

    //执行点击
    public void click(String action) {
        if (TextUtils.isEmpty(action)) {
            return;
        }
        if (!TextUtils.isEmpty(action)) {
            JSONObject jsonObject = null;
            try {
                jsonObject = new JSONObject();
                jsonObject.put("action",action);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            // 埋点  大转盘
            XyyIoUtil.track(XyyIoUtil.ACTION_HOME_ROTARYTABLE,jsonObject);
//            RoutersUtils.open(action);
            HashMap<String,String> mParams = new HashMap<>();
            mParams.put("entrance", "首页(浮窗)");
            action = JGTrackTopLevelKt.splicingUrlWithParams(action,mParams);

            RoutersUtils.openForResult(action, 1010);
            if (mAnalysisCallback != null) {
                mAnalysisCallback.onTurnTableClickCallback(action);
            }
            if (mBigWheelAnalysisCallback != null) {
                mBigWheelAnalysisCallback.onAlertClickCallback(items.trackData);
            }


            JSONObject mJsonObject = null;
            try {
                mJsonObject = new JSONObject();
                mJsonObject.put(TrackManager.FIELD_URL,action);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            //新的首页埋点
            XyyIoUtil.track(TrackManager.TrackHome.EVENT_ACTION_FLOAT_CLICK,mJsonObject);

            if (mContext !=null){
                JGTrackTopLevelKt.jgTrackHomeBtnClick(mContext, JGTrackManager.TrackHomePage.TRACK_HOME_OUT_URL,"浮窗","浮窗");
            }
        } else {
            setVisibility(View.GONE);
        }
    }
}
