package com.ybmmarket20.view.couponrelatedgoods

import android.annotation.SuppressLint
import android.widget.CheckBox
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.SearchFilterBean
import com.ybmmarket20.view.BaseFilterPopWindow
import com.ybmmarket20.view.homesteady.whenAllNotNull

/**
 * 综合排序PopupWindow
 */

//综合排序类型-默认
const val COMPREHENSIVE_TYPE_DEFAULT = "1"
//综合排序类型-销量
const val COMPREHENSIVE_TYPE_SALES_VOLUME = "2"
//综合排序类型-价格
const val COMPREHENSIVE_TYPE_PRICE = "3"

class ComprehensiveSortPopupWindow : BaseFilterPopWindow() {

    private var rvSynthesize: RecyclerView? = null
    private var mClickItemListener: IComprehensiveSortListener? = null
    private var mSelectedData: ComprehensiveItem? = null

    override fun getLayoutId() = R.layout.pop_layout_synthesize

    override fun initView() {
        rvSynthesize = getView(R.id.rv_synthesize)
        setOnSelectListener(object: OnSelectListener{
            override fun getValue(show: SearchFilterBean?) {}

            override fun OnDismiss(multiSelectStr: String?) {
                mClickItemListener?.onCancel(mSelectedData)
            }
        })
    }

    fun setData(itemList: List<ComprehensiveItem>) {
        rvSynthesize?.adapter = SynthesizeAdapter(R.layout.item_pop_synthesize, itemList)
        rvSynthesize?.layoutManager = LinearLayoutManager(rvSynthesize?.context)
    }

    fun getDataList1(): List<ComprehensiveItem> = listOf(
        ComprehensiveItem("默认", "排序",  COMPREHENSIVE_TYPE_DEFAULT, true),
        ComprehensiveItem("按销量由高到低", "销量", COMPREHENSIVE_TYPE_SALES_VOLUME),
        ComprehensiveItem("按价格由低到高", "价格", COMPREHENSIVE_TYPE_PRICE)
    )

    fun setOnItemClickListener(clickItemListener: IComprehensiveSortListener) {
        mClickItemListener = clickItemListener
    }


    data class ComprehensiveItem(
        val text: String,
        val textResult: String,
        val id: String,
        var isSelected: Boolean = false
    )

    interface IComprehensiveSortListener {
        fun onItemClick(item: ComprehensiveItem?)

        fun onCancel(item: ComprehensiveItem?)
    }


    inner class SynthesizeAdapter(layoutResId: Int, data: List<ComprehensiveItem>?) :
        YBMBaseAdapter<ComprehensiveItem>(layoutResId, data) {

        @SuppressLint("NotifyDataSetChanged")
        override fun bindItemView(baseViewHolder: YBMBaseHolder, t: ComprehensiveItem?) {
            whenAllNotNull(baseViewHolder, t) { holder, bean ->
                holder.setText(R.id.tv_item_text, bean.text)
                val cbItem: CheckBox = baseViewHolder.getView(R.id.cb_item)
                cbItem.isChecked = bean.isSelected
                holder.itemView.setOnClickListener {
                    <EMAIL> {
                        if (it is ComprehensiveItem) it.isSelected = false
                    }
                    bean.isSelected = true
                    notifyDataSetChanged()
                    mSelectedData = bean
                    mClickItemListener?.onItemClick(bean)
                    dismiss()
                }
            }
        }
    }

}