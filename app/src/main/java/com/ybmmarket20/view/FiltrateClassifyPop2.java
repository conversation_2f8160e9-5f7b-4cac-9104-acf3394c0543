package com.ybmmarket20.view;

import static com.ybmmarket20.search.BaseSearchProductActivity.PAGE_TYPE_OP;
import static com.ybmmarket20.view.searchFilter.view.SearchDynamicLabelViewKt.DYNAMIC_LABEL_STYLE_GRID;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Message;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.activity.ComponentActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.apkfuns.logutils.LogUtils;
import com.google.gson.reflect.TypeToken;
import com.luck.picture.lib.tools.ScreenUtils;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.utils.JsonUtils;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.FiltrateClassManufacturerAdapter;
import com.ybmmarket20.adapter.FiltrateClassShopAdapter;
import com.ybmmarket20.adapter.FiltrateClassSpecAdapter;
import com.ybmmarket20.bean.ManufacturersBean;
import com.ybmmarket20.bean.SearchDynamicLabelConfig;
import com.ybmmarket20.bean.SearchFilterBean;
import com.ybmmarket20.common.JGTrackManager;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.search.BaseSearchProductActivity;
import com.ybmmarket20.utils.CopyUtilKt;
import com.ybmmarket20.utils.DrawableUtil;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.searchFilter.view.SearchDynamicLabelExtView;
import com.ybmmarket20.view.searchFilter.view.SearchDynamicLabelView;
import com.ybmmarket20.view.searchFilter.view.SearchFilterNearEffectiveView;
import com.ybmmarket20.viewmodel.SearchDataViewModel;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.functions.Function2;
import kotlin.jvm.functions.Function3;

/**
 * 全部药品筛选-一级分类
 */

public class FiltrateClassifyPop2 {

    private LeftPopWindow mLeftPopWindow;
    private ManufacturersPop mManufacturersPop;
    private LeftPopWindow.Listener<SearchFilterBean> listener;

    private TextView mTvDetails01, mTvDetails02, mTvDetails03, tvAllManufacture;
    private LinearLayout mLl01, mLl02, mLl03, mLl04;
    private EditText mPriceRangeFloor, mPriceRangeTop;
    private TextView mTvAvailable, mTvPromotion, mTvPriceRangeFloor, mTvPriceRangeTop, mTvDpby, mTvSpellGroupAndPgby;

    private boolean isAvailable = false, isPromotion = false
            , isClassA = false, isClassB = false
            , isClassRx = false, isClassElse = false
            , isCanUseCoupon = false, isDpby = false, isSpellGroupAndPgby = false;
    private boolean isShunfeng = false;
    private boolean isJD = false;
    private String key = "";
    private String value = "";
    protected String lastName = "全部厂家";
    private List<String> mList = new ArrayList<>();
    private TextView mClassA;
    private TextView mClassB;
    private TextView mClassRx;
    private TextView mClassElse;
    private int minSlop;
    private int lastAction;
    private float lastY;
    private InputMethodManager inputManager;
    private TextView tvYBM;
    private LinearLayout llYBM;
    private boolean isPlan;
    private String planId;
    private TextView tv_price_range;
    private LinearLayout ll_price_range;
    private LinearLayout llManufactureSelected;

    private  View mShopServiceTitle;
    private  View mShopServiceOptions;
    private  TextView mShopServiceShunfeng;
    private  TextView mShopServiceJd;
    private  TextView mCanUseCoupon;

    //规格
    private TextView tvSpecSelectedContent;
    private TextView tvSpecCollapse;
    private RecyclerView rvSpec;
    private ConstraintLayout clSpec;
    //生产厂家
    private TextView tvManufacturerSelectedContent;
    private TextView tvManufacturerCollapse;
    private RecyclerView rvManufacturer;
    private ConstraintLayout clManufacturer;
    //商家
    private TextView tvShopSelectedContent;
    private TextView tvShopCollapse;
    private RecyclerView rvShop;
    private ConstraintLayout clShop;
    //动态标签
    private SearchDynamicLabelView dynamicLabel;
    private LinearLayout lyDynamicLabelCnMedine;
    private Map<String,Boolean> dynamicLabelExtCollaspeMap;

    // 商品规格,多个以逗号','分割
    private String spec;
    // 店铺code 多个以逗号分隔
    private String shopCodes;

    private RelativeLayout rlAllManufacture;

    //规格
    private List<SearchFilterBean> mSpecList;
    private List<String> mSelectedSpecList = new ArrayList<>();
    private boolean isSpecCollapse = false;
    //店铺
    private List<SearchFilterBean> mShopList;
    private List<String> mSelectedShopList = new ArrayList<>();
    private List<String> mSelectedShopKeyList = new ArrayList<>();
    private boolean isShopCollapse = false;
    //生产厂家
    private List<ManufacturersBean> mManufacturerList = new ArrayList<>();
    private List<String> mSelectedManufacturerList = new ArrayList<>();
    private boolean isManufacturerCollapse = false;
    //有效期
    private SearchFilterNearEffectiveView sfnev;
    private String mNearEffective = null;
    private String mNearEffectiveStr = null;
//    private String mTempNearEffective = null;
    // 中药扩展筛选
    private LinearLayout lyDynamicCnMedine;
    //是否展示单品包邮按钮
    private boolean mIsShowDpby = true;

    private SearchDataViewModel mVm;
    //存储动态标签选中结果
    private Map<String, String> dynamicLabelResultMap = new HashMap<>();
    //动态标签配置
    private List<SearchDynamicLabelConfig> mDynamicLabelConfig;
    private List<SearchDynamicLabelConfig> mDynamicLabelConfigTemp;

    //样式
    private int mPageType = PAGE_TYPE_OP;

    public void setSpec(List<SearchFilterBean> specList, String selectedSpec) {
        try {
            mSpecList = CopyUtilKt.deepCopy(specList);
        } catch (IOException | ClassNotFoundException e) {
            mSpecList = new ArrayList<>();
            e.printStackTrace();
        }
        if (!TextUtils.isEmpty(selectedSpec)) {
            String[] specArray = selectedSpec.split(",");
            mSelectedSpecList = new ArrayList<>(Arrays.asList(specArray));
            Iterator<String> iterator = mSelectedSpecList.iterator();
            //匹配列表和已选中项，不存在则清除掉
            while (iterator.hasNext()) {
                String currStr = iterator.next();
                boolean isContain = false;
                for (SearchFilterBean searchFilterBean : mSpecList) {
                    if (TextUtils.equals(searchFilterBean.key, currStr)) {
                        isContain = true;
                        break;
                    }
                }
                if (!isContain) {
                    iterator.remove();
                }
            }
        } else {
            mSelectedSpecList.clear();
        }
    }

    public void setShops(List<SearchFilterBean> shopList, String selectedShop) {
        try {
            mShopList = CopyUtilKt.deepCopy(shopList);
        } catch (IOException | ClassNotFoundException e) {
            mShopList = new ArrayList<>();
            e.printStackTrace();
        }
        if (!TextUtils.isEmpty(selectedShop)) {
            String[] shopArray = selectedShop.split(",");
            mSelectedShopKeyList = new ArrayList<>(Arrays.asList(shopArray));

            Iterator<String> iterator = mSelectedShopKeyList.iterator();
            //匹配列表和已选中项，不存在则清除掉
            while (iterator.hasNext()) {
                String currStr = iterator.next();
                boolean isContain = false;
                for (SearchFilterBean searchFilterBean : mShopList) {
                    if (TextUtils.equals(searchFilterBean.showName, currStr)) {
                        isContain = true;
                        break;
                    }
                }
                if (!isContain) {
                    iterator.remove();
                }
            }
            mSelectedShopList.clear();
            for (SearchFilterBean searchFilterBean : shopList) {
                if (searchFilterBean.isSelected) {
                    mSelectedShopList.add(searchFilterBean.showName);
                }
            }
        } else {
            mSelectedShopKeyList.clear();
            mSelectedShopList.clear();
        }
    }

    public void setManufacturer(List<ManufacturersBean> manufacturerList, List<String> selectedManufacturerList) {
        try {
            mManufacturerList = CopyUtilKt.deepCopy(manufacturerList);
            mSelectedManufacturerList = CopyUtilKt.deepCopy(selectedManufacturerList);
        } catch (IOException | ClassNotFoundException e) {
            e.printStackTrace();
        }
        Iterator<String> iterator = mSelectedManufacturerList.iterator();
        //匹配列表和已选中项，不存在则清除掉
        while (iterator.hasNext()) {
            String currStr = iterator.next();
            boolean isContain = false;
            for (ManufacturersBean manufacturersBean : mManufacturerList) {
                if (TextUtils.equals(manufacturersBean.manufacturer, currStr)) {
                    isContain = true;
                    break;
                }
            }
            if (!isContain) {
                iterator.remove();
            }
        }
        for (ManufacturersBean manufacturersBean : mManufacturerList) {
            manufacturersBean.isSelected = mSelectedManufacturerList.contains(manufacturersBean.manufacturer);
        }
    }

    /**
     * 隐藏店铺服务
     * @param isHidden
     */
    public void hiddenShopService(boolean isHidden) {
        if (mLeftPopWindow != null) {
            mLeftPopWindow.hiddenShopService(isHidden);
        }
    }

    /**
     * 仅看有货
     */
    public void setAvailable(boolean isAvailable) {
        this.isAvailable = isAvailable;
        handler.sendMessage(handler.obtainMessage(20));
    }

    /**
     * 单品包邮
     * @param isDpby
     */
    public void setDpby(boolean isDpby) {
        this.isDpby = isDpby;
        handler.sendMessage(handler.obtainMessage(100));
    }

    /**
     * 拼团包邮
     * @param isSpellGroupAndPgby
     */
    public void setSpellGroupAndPgby(boolean isSpellGroupAndPgby) {
        this.isSpellGroupAndPgby = isSpellGroupAndPgby;
        this.isDpby = isSpellGroupAndPgby;
        handler.sendMessage(handler.obtainMessage(110));
    }

    public void setDynamicLabelSelectedMap(Map<String, String> dynamicLabelSelectedMap) {
        this.dynamicLabelResultMap = dynamicLabelSelectedMap;
    }

    public void setDynamicConfig(List<SearchDynamicLabelConfig> dynamicLabelConfig) {
        mDynamicLabelConfig = dynamicLabelConfig;
    }

    public void setCanUseCoupon(boolean isCanUseCoupon) {
        this.isCanUseCoupon = isCanUseCoupon;
        handler.sendMessage(handler.obtainMessage(90));
    }

    public void setExpressAll(boolean isExpress) {
        isJD = isExpress;
        isShunfeng = isExpress;
        mShopServiceShunfeng.setActivated(isExpress);
        mShopServiceJd.setActivated(isExpress);
        handler.sendMessage(handler.obtainMessage(80));
    }

    public void setExpress(boolean isSelectedJD, boolean isSelectedSF) {
        isJD = isSelectedJD;
        isShunfeng = isSelectedSF;
        mShopServiceShunfeng.setActivated(isSelectedSF);
        mShopServiceJd.setActivated(isSelectedJD);
    }

    /**
     * 仅看有货
     */
    public void setPromotion(boolean isPromotion) {
        this.isPromotion = isPromotion;
        handler.sendMessage(handler.obtainMessage(30));
    }

    /*
     * 厂家
     * */
    public void setLastNames(List<String> lastNames) {
        handler.sendMessage(handler.obtainMessage(10, lastNames));
    }

    public void setDataType(String key, String value) {
        if (this.key.equals(key) && this.value.equals(value)) {
            return;
        } else {
            this.key = key;
            this.value = value;
        }
    }

    // 商品规格,多个以逗号','分割
    public void setSpec(String spec) {
        this.spec = spec;
    }

    // 店铺code 多个以逗号分隔
    public void setSelectedShopCodes(String shopCodes) {
        this.shopCodes = shopCodes;
    }

    private Context mContext;
    public FiltrateClassifyPop2(Context context, boolean isShowDpby) {
        mIsShowDpby = isShowDpby;
        mContext = context;
        init();
    }

    public FiltrateClassifyPop2(Context context, boolean isShowDpby, int styleType) {
        mIsShowDpby = isShowDpby;
        mContext = context;
        mPageType = styleType;
        init();
    }

    public FiltrateClassifyPop2(String planId) {
        init();
        this.planId = planId;
        isPlan = true;
    }

    /**
     * 从商品搜索过来，需要隐藏（全部生产厂家）
     */
    public void hideManufactureTips() {
        if (tvAllManufacture != null) {
            rlAllManufacture.setVisibility(View.GONE);
        }
    }

    /**
     * 初始化
     */
    private void init() {

        mLeftPopWindow = new LeftPopWindow(R.layout.pop_filtrate_classify2) {

            @Override
            protected void initView(View contentView) {
                tvYBM = contentView.findViewById(R.id.tv_ybm);
                llYBM = contentView.findViewById(R.id.ll_ybm);
                ImageView ivBack = (ImageView) contentView.findViewById(R.id.iv_back);
                TextView tvTitle =
                        (TextView) contentView.findViewById(R.id.tv_title);
                Button btnReset = (Button) contentView.findViewById(R.id.btn_reset);
                Button btnAffirm = (Button) contentView.findViewById(R.id.btn_affirm);
                rlAllManufacture = (RelativeLayout) contentView.findViewById(R.id.rl_all_manufacture);

                mTvDetails01 = (TextView) contentView.findViewById(R.id.tv_details_01);
                mTvDetails02 = (TextView) contentView.findViewById(R.id.tv_details_02);
                mTvDetails03 = (TextView) contentView.findViewById(R.id.tv_details_03);
                tvAllManufacture = contentView.findViewById(R.id.tv_all_manufacture);
                mLl01 = (LinearLayout) contentView.findViewById(R.id.ll_01);
                mLl02 = (LinearLayout) contentView.findViewById(R.id.ll_02);
                mLl03 = (LinearLayout) contentView.findViewById(R.id.ll_03);
                mLl04 = (LinearLayout) contentView.findViewById(R.id.ll_04);

                mTvAvailable = (TextView) contentView.findViewById(R.id.tv_available);
                mTvPromotion = (TextView) contentView.findViewById(R.id.tv_promotion);

                mPriceRangeFloor = (EditText) contentView.findViewById(R.id.price_range_floor);
                mPriceRangeTop = (EditText) contentView.findViewById(R.id.price_range_top);

                mTvPriceRangeFloor = (TextView) contentView.findViewById(R.id.tv_price_range_floor);
                mTvPriceRangeTop = (TextView) contentView.findViewById(R.id.tv_price_range_top);
                mTvDpby = contentView.findViewById(R.id.tvDpby);
                mTvSpellGroupAndPgby = contentView.findViewById(R.id.tvSpellGroupAndPgby);
                tv_price_range = contentView.findViewById(R.id.tv_price_range);
                ll_price_range = contentView.findViewById(R.id.ll_price_range);
                llManufactureSelected = contentView.findViewById(R.id.ll_manufacture_selected);

                mShopServiceTitle = contentView.findViewById(R.id.shop_service_title);
                mShopServiceOptions = contentView.findViewById(R.id.shop_service_options);
                mShopServiceShunfeng = contentView.findViewById(R.id.express_shunfeng);
                mShopServiceJd = contentView.findViewById(R.id.express_jd);

                //规格
                tvSpecSelectedContent = contentView.findViewById(R.id.tvSpecSelectedContent);
                tvSpecCollapse = contentView.findViewById(R.id.tvSpecCollapse);
                rvSpec = contentView.findViewById(R.id.rvSpec);
                clSpec = contentView.findViewById(R.id.clSpec);
                //生产厂家
                tvManufacturerSelectedContent = contentView.findViewById(R.id.tvManufacturerSelectedContent);
                tvManufacturerCollapse = contentView.findViewById(R.id.tvManufacturerCollapse);
                rvManufacturer = contentView.findViewById(R.id.rvManufacturer);
                clManufacturer = contentView.findViewById(R.id.clManufacturer);
                //商家
                tvShopSelectedContent = contentView.findViewById(R.id.tvShopSelectedContent);
                tvShopCollapse = contentView.findViewById(R.id.tvShopCollapse);
                rvShop = contentView.findViewById(R.id.rvShop);
                clShop = contentView.findViewById(R.id.clShop);
                //可用券
                mCanUseCoupon = contentView.findViewById(R.id.tvCanUseCoupon);
                //有效期
                sfnev = contentView.findViewById(R.id.sfnev);
                //动态标签
                dynamicLabel = contentView.findViewById(R.id.dynamicLabel);
                lyDynamicLabelCnMedine = contentView.findViewById(R.id.lyDynamicLabelCnMedine);

                View placeHolder = contentView.findViewById(R.id.view_bg);
                LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) placeHolder.getLayoutParams();
                lp.height = (int)(ScreenUtils.getScreenHeight(mContext) * 0.17);
                placeHolder.setLayoutParams(lp);

                if (!mIsShowDpby) {
                    mTvDpby.setVisibility(View.GONE);
                }

                if (mPageType == PAGE_TYPE_OP) {
                    mShopServiceOptions.setVisibility(View.GONE);
                    mTvDpby.setVisibility(View.GONE);
                    mTvSpellGroupAndPgby.setVisibility(View.GONE);
                } else {
                    dynamicLabel.setVisibility(View.GONE);
                }

                boolean a = isAvailable;
                mShopServiceShunfeng.setOnClickListener(view -> {
                    boolean activated = view.isActivated();
                    isShunfeng = !activated;
                    view.setActivated(!activated);
                    BaseSearchProductActivity.jgBtnClick(mContext,"顺丰快递","筛选弹窗");
                });
                mShopServiceJd.setOnClickListener(view -> {
                    boolean activated = view.isActivated();
                    isJD = !activated;
                    view.setActivated(!activated);
                    BaseSearchProductActivity.jgBtnClick(mContext,"京东快递","筛选弹窗");
                });

                mPriceRangeFloor.addTextChangedListener(new TextWatcher() {
                    @Override
                    public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                    }

                    @Override
                    public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                        if (TextUtils.isEmpty(charSequence)) {
                            mTvPriceRangeFloor.setVisibility(View.VISIBLE);
                        } else {
                            mTvPriceRangeFloor.setVisibility(View.GONE);
                        }
                    }

                    @Override
                    public void afterTextChanged(Editable editable) {
                    }
                });
                mPriceRangeTop.addTextChangedListener(new TextWatcher() {
                    @Override
                    public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                    }

                    @Override
                    public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                        if (TextUtils.isEmpty(charSequence)) {
                            mTvPriceRangeTop.setVisibility(View.VISIBLE);
                        } else {
                            mTvPriceRangeTop.setVisibility(View.GONE);
                        }
                    }

                    @Override
                    public void afterTextChanged(Editable editable) {
                    }
                });

                mClassA = (TextView) contentView.findViewById(R.id.class_a);
                mClassB = (TextView) contentView.findViewById(R.id.class_b);
                mClassRx = (TextView) contentView.findViewById(R.id.class_rx);
                mClassElse = (TextView) contentView.findViewById(R.id.class_else);

                tvTitle.setText("筛选");
                ivBack.setImageResource(R.drawable.icon_close);

                new DrawableUtil(mTvDetails01, new DrawableUtil.OnDrawableListener() {
                    @Override
                    public void onLeft(View v, Drawable left) {

                    }

                    @Override
                    public void onRight(View v, Drawable right) {
                        mLl01.setVisibility(View.GONE);
                        mLl04.setVisibility(View.GONE);
                        if (mTvDetails01 != null && mTvDetails01.getText() != null && mManufacturersPop != null) {
                            mManufacturersPop.removeKey(mTvDetails01.getText().toString());
                            mList.remove(mTvDetails01.getText().toString());
                            mTvDetails01.setText("");
                        }
                    }
                });

                new DrawableUtil(mTvDetails02, new DrawableUtil.OnDrawableListener() {
                    @Override
                    public void onLeft(View v, Drawable left) {

                    }

                    @Override
                    public void onRight(View v, Drawable right) {
                        mLl02.setVisibility(View.GONE);
                        mLl04.setVisibility(View.GONE);
                        if (mTvDetails02 != null) {
                            mManufacturersPop.removeKey(mTvDetails02.getText().toString());
                            mList.remove(mTvDetails02.getText().toString());
                            mTvDetails02.setText("");
                        }
                    }
                });
                new DrawableUtil(mTvDetails03, new DrawableUtil.OnDrawableListener() {
                    @Override
                    public void onLeft(View v, Drawable left) {

                    }

                    @Override
                    public void onRight(View v, Drawable right) {
                        mLl03.setVisibility(View.GONE);
                        mLl04.setVisibility(View.GONE);
                        if (mTvDetails03 != null) {
                            mManufacturersPop.removeKey(mTvDetails03.getText().toString());
                            mList.remove(mTvDetails03.getText().toString());
                            mTvDetails03.setText("");
                        }
                    }
                });

                //顶部title高度

                //仅看有货
                mTvAvailable.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        boolean activated = v.isActivated();
                        v.setActivated(!activated);
                        isAvailable = !activated;
                        BaseSearchProductActivity.jgBtnClick(mContext,"仅看有货","筛选弹窗");
                    }
                });
                mTvDpby.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        boolean activated = v.isActivated();
                        v.setActivated(!activated);
                        isDpby = !activated;
                        BaseSearchProductActivity.jgBtnClick(mContext,"单品包邮","筛选弹窗");
                    }
                });

                mTvSpellGroupAndPgby.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        XyyIoUtil.track("action_Search_GroupPurchaseFreeShopping");
                        JGTrackManager.Companion.eventTrack(mContext, "action_Search_GroupPurchaseFreeShopping", new HashMap<>());
                        boolean activated = v.isActivated();
                        v.setActivated(!activated);
                        mTvDpby.setActivated(!activated);
                        isDpby = !activated;
                        isSpellGroupAndPgby = !activated;
                        BaseSearchProductActivity.jgBtnClick(mContext,"拼团包邮","筛选弹窗");
                    }
                });

                //有促销
                mTvPromotion.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        boolean activated = v.isActivated();
                        v.setActivated(!activated);
                        isPromotion = !activated;
                        BaseSearchProductActivity.jgBtnClick(mContext,"促销类型","筛选弹窗");
                    }
                });

                //可用券
                mCanUseCoupon.setOnClickListener(view -> {
                    boolean activated = view.isActivated();
                    view.setActivated(!activated);
                    isCanUseCoupon = !activated;
                    XyyIoUtil.track("available_voucher_screening_options");
                    BaseSearchProductActivity.jgBtnClick(mContext,"可用券","筛选弹窗");
                });

                //甲类otc
                mClassA.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        boolean activated = v.isActivated();
                        v.setActivated(!activated);
                        isClassA = !activated;
                        BaseSearchProductActivity.jgBtnClick(mContext,"药品类型","筛选弹窗");
                    }
                });
                //乙类otc
                mClassB.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        boolean activated = v.isActivated();
                        v.setActivated(!activated);
                        isClassB = !activated;
                        BaseSearchProductActivity.jgBtnClick(mContext,"药品类型","筛选弹窗");
                    }
                });
                //处方药rx
                mClassRx.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        boolean activated = v.isActivated();
                        v.setActivated(!activated);
                        isClassRx = !activated;
                        BaseSearchProductActivity.jgBtnClick(mContext,"药品类型","筛选弹窗");
                    }
                });
                //其他
                mClassElse.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        boolean activated = v.isActivated();
                        v.setActivated(!activated);
                        isClassElse = !activated;
                        BaseSearchProductActivity.jgBtnClick(mContext,"药品类型","筛选弹窗");
                    }
                });
                //返回键
                contentView.findViewById(R.id.iv_back).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        getResult();
                    }
                });

                View.OnClickListener resultOnClickListener = v -> {
                    mLeftPopWindow.dismiss(true);
                    if (listener != null) {
                        listener.onDismiss();
                    }
                };
                //左边空白区域
                contentView.findViewById(R.id.view_bg).setOnClickListener(resultOnClickListener);
                //点击关闭按钮
                contentView.findViewById(R.id.ivSearchFilterClose).setOnClickListener(resultOnClickListener);
                btnReset.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        reset(false);
                        BaseSearchProductActivity.jgBtnClick(mContext,"重置","筛选弹窗");
                    }
                });

                btnAffirm.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        mVm.updateSearchDynamicLabelSelected(dynamicLabelResultMap);
                        getResult();
                        BaseSearchProductActivity.jgBtnClick(mContext,"确认","筛选弹窗");
                    }
                });
                rlAllManufacture.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                        initManufacture();

                        String[] strings = setPriceRange();

                        if (mList == null) {
                            mList = new ArrayList<>();
                        }
                        if (mList.isEmpty()) {
                            mList.add(lastName);
                        }
                        mManufacturersPop.setDataType(key, value, isAvailable, isPromotion, isClassA, isClassB, isClassRx, isClassElse, spec, shopCodes, strings[0], strings[1], mList);
                        mManufacturersPop.show();
                    }
                });

                dynamicLabel.setItemSelectCallback((stringStringMap, config) -> {
                    dynamicLabelResultMap = stringStringMap;
                    return null;
                });

                sfnev.setOnSelectedItemCallback(s -> {
                    if (s.isSelected()) {
                        mNearEffective = s.getNearEffectiveId();
                        mNearEffectiveStr = s.getText();
                    } else {
                        mNearEffective = null;
                        mNearEffectiveStr = null;
                    }
                    return null;
                });

            }
        };
        mLeftPopWindow.setClassify(true);
        mLeftPopWindow.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                isScroll(event);
                return false;
            }
        });
    }

    /**
     * 设置viewmodel
     * @param vm
     */
    public void setSearchViewModel(SearchDataViewModel vm) {
        mVm = vm;
        dynamicLabel.setData(vm, DYNAMIC_LABEL_STYLE_GRID);
        mVm.getDynamicLabelConfigLiveData().observe((ComponentActivity) mContext, dynamicLabelConfigs -> {
            lyDynamicLabelCnMedine.removeAllViews();
            for (SearchDynamicLabelConfig config : dynamicLabelConfigs) {
                if (SearchDynamicLabelConfig.TYPE_POP == config.getLabelType()) {
                    SearchDynamicLabelExtView itemView = new SearchDynamicLabelExtView(mContext);
                    itemView.setCollapseListener((s, aBoolean) -> {
                        if(dynamicLabelExtCollaspeMap == null){
                            dynamicLabelExtCollaspeMap = new HashMap<>();
                        }
                        dynamicLabelExtCollaspeMap.put(s,aBoolean);
                        return null;
                    });
                    itemView.setOnSelectedItemCallback((currentConfig, selectItems) -> {
                        if(currentConfig != null){
                            if(1 == currentConfig.isDynamicParam()){
                                if(dynamicLabelResultMap.containsKey(currentConfig.getParamKey())){
                                    // 包含了某种扩展筛选
                                    String mapStr = dynamicLabelResultMap.get(currentConfig.getParamKey()); // 扩展选中字符串
                                    HashMap<String,List<String>> extMap = JsonUtils.fromJson(mapStr,new TypeToken<HashMap<String,List<String>>>(){}.getType());   // 扩展选中对象
                                    extMap.put(currentConfig.getDynamicParamKey(),selectItems); // 直接覆盖或插入当前扩展选中
                                    dynamicLabelResultMap.put(currentConfig.getParamKey(),JsonUtils.toJson(extMap));    // 覆盖
                                }else{
                                    // 不包含任何扩展筛选
                                    HashMap<String,List<String>> extMap = new HashMap<>();
                                    extMap.put(currentConfig.getDynamicParamKey(),selectItems);
                                    dynamicLabelResultMap.put(currentConfig.getParamKey(),JsonUtils.toJson(extMap));
                                }
                            }else{
                                dynamicLabelResultMap.put(currentConfig.getParamKey(),JsonUtils.toJson(selectItems));
                            }
                        }
                        return null;
                    });
                    if(dynamicLabelExtCollaspeMap != null){
                        itemView.setData(mVm,config,dynamicLabelExtCollaspeMap.get(config.getLabelKey()));
                    }else{
                        itemView.setData(mVm,config,false);
                    }
                    lyDynamicLabelCnMedine.addView(itemView,new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
                }
            }
        });
    }

    //设置规格
    private void setSpecList() {
        if (mSpecList == null || mSpecList.isEmpty()) {
            clSpec.setVisibility(View.GONE);
            rvSpec.setVisibility(View.GONE);
            return;
        } else {
            clSpec.setVisibility(View.VISIBLE);
            rvSpec.setVisibility(View.VISIBLE);
        }
        tvSpecSelectedContent.setText(getSelectedStrWithTitle(mSelectedSpecList));
        rvSpec.setLayoutManager(new GridLayoutManager(mContext, 3));
        List<SearchFilterBean> specList = new ArrayList<>();
        if (mSpecList.size() > 9) {
            tvSpecCollapse.setVisibility(View.VISIBLE);
            if (isSpecCollapse) {
                specList.addAll(mSpecList);
            } else {
                for (int i = 0; i < 9; i++) {
                    specList.add(mSpecList.get(i));
                }
            }
        } else {
            tvSpecCollapse.setVisibility(View.INVISIBLE);
            specList.addAll(mSpecList);
        }
        FiltrateClassSpecAdapter adapter = new FiltrateClassSpecAdapter(R.layout.item_pop_specfication2, specList);
        rvSpec.setAdapter(adapter);
        adapter.setOnClickListener(new Function2<String, Boolean, Unit>() {
            @Override
            public Unit invoke(String str, Boolean isSelected) {
                if (isSelected) {
                    mSelectedSpecList.add(str);
                } else {
                    mSelectedSpecList.remove(str);
                }
                tvSpecSelectedContent.setText(getSelectedStrWithTitle(mSelectedSpecList));
                BaseSearchProductActivity.jgBtnClick(mContext,"规格","筛选弹窗");
                return null;
            }
        });
        setArrow(tvSpecCollapse, isSpecCollapse);
        tvSpecCollapse.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                isSpecCollapse = !isSpecCollapse;
                setSpecList();
                setArrow(tvSpecCollapse, isSpecCollapse);
            }
        });
    }

    private void setShopList() {
        if (mShopList == null || mShopList.isEmpty()) {
            clShop.setVisibility(View.GONE);
            rvShop.setVisibility(View.GONE);
            return;
        } else {
            clShop.setVisibility(View.VISIBLE);
            rvShop.setVisibility(View.VISIBLE);
        }
        tvShopSelectedContent.setText(getSelectedStrWithTitle(mSelectedShopList));
        rvShop.setLayoutManager(new GridLayoutManager(mContext, 2));

        List<SearchFilterBean> shopList = new ArrayList<>();
        if (mShopList.size() > 6) {
            tvShopCollapse.setVisibility(View.VISIBLE);
            if (isShopCollapse) {
                shopList.addAll(mShopList);
            } else {
                for (int i = 0; i < 6; i++) {
                    shopList.add(mShopList.get(i));
                }
            }
        } else {
            tvShopCollapse.setVisibility(View.INVISIBLE);
            shopList.addAll(mShopList);
        }

        FiltrateClassShopAdapter adapter = new FiltrateClassShopAdapter(R.layout.item_pop_shop, shopList);
        rvShop.setAdapter(adapter);
        adapter.setOnClickListener(new Function3<String, String, Boolean, Unit>() {
            @Override
            public Unit invoke(String str, String key, Boolean isSelected) {
                if (isSelected) {
                    mSelectedShopList.add(str);
                    mSelectedShopKeyList.add(key);
                } else {
                    mSelectedShopList.remove(str);
                    mSelectedShopKeyList.remove(key);
                }
                tvShopSelectedContent.setText(getSelectedStrWithTitle(mSelectedShopList));
                BaseSearchProductActivity.jgBtnClick(mContext,"商家","筛选弹窗");
                return null;
            }
        });
        setArrow(tvShopCollapse, isShopCollapse);
        tvShopCollapse.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                isShopCollapse = !isShopCollapse;
                setShopList();
                setArrow(tvShopCollapse, isShopCollapse);
            }
        });
    }

    private void setManufacturerList() {
        if (mManufacturerList == null || mManufacturerList.isEmpty()) {
            clManufacturer.setVisibility(View.GONE);
            rvManufacturer.setVisibility(View.GONE);
            return;
        } else {
            clManufacturer.setVisibility(View.VISIBLE);
            rvManufacturer.setVisibility(View.VISIBLE);
        }
        tvManufacturerSelectedContent.setText(getSelectedStrWithTitle(mSelectedManufacturerList));
        rvManufacturer.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false));
        List<ManufacturersBean> manufacturerList = new ArrayList<>();
        if (mManufacturerList.size() > 3) {
            tvManufacturerCollapse.setVisibility(View.VISIBLE);
            if (isManufacturerCollapse) {
                manufacturerList.addAll(mManufacturerList);
            } else {
                for (int i = 0; i < 3; i++) {
                    manufacturerList.add(mManufacturerList.get(i));
                }
            }
        } else {
            tvManufacturerCollapse.setVisibility(View.INVISIBLE);
            manufacturerList.addAll(mManufacturerList);
        }
        FiltrateClassManufacturerAdapter adapter = new FiltrateClassManufacturerAdapter(R.layout.item_pop_specfication2, manufacturerList);
        rvManufacturer.setAdapter(adapter);
        adapter.setOnClickListener(new Function2<String, Boolean, Unit>() {
            @Override
            public Unit invoke(String str, Boolean isSelected) {
                if (isSelected) {
                    mSelectedManufacturerList.add(str);
                } else {
                    mSelectedManufacturerList.remove(str);
                }
                tvManufacturerSelectedContent.setText(getSelectedStrWithTitle(mSelectedManufacturerList));
                BaseSearchProductActivity.jgBtnClick(mContext,"厂家","筛选弹窗");
                return null;
            }
        });
        setArrow(tvManufacturerCollapse, isManufacturerCollapse);
        tvManufacturerCollapse.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                isManufacturerCollapse = !isManufacturerCollapse;
                setManufacturerList();
                setArrow(tvManufacturerCollapse, isManufacturerCollapse);
            }
        });
    }

    private void setArrow(TextView tv, boolean isCollapse) {
        Drawable drawable = mContext.getResources().getDrawable(!isCollapse?R.drawable.icon_search_filter_title_arrow_down: R.drawable.icon_search_filter_title_arrow_up);
        tv.setText(!isCollapse? "展开" : "收起");
        drawable.setBounds(0,0,drawable.getMinimumWidth(),drawable.getMinimumHeight());
        tv.setCompoundDrawables(null, null, drawable, null);
    }

    private String getSelectedStr(List<String> list) {
        String str = "";
        for (int i = 0; i < list.size(); i++) {
            str = str + list.get(i);
            if (i != list.size() - 1) {
                str += ",";
            }
        }
        return str;
    }

    private String getSelectedStrWithTitle(List<String> list) {
        String str = getSelectedStr(list);
        if (TextUtils.isEmpty(str)) {
            return "";
        } else {
            return "已选: " + str;
        }
    }

    /**
     * 设置是否隐藏间隔区间选项
     *
     * @param isHidden
     */
    public void hiddenPriceRange(boolean isHidden) {
        if (isHidden) {
            tv_price_range.setVisibility(View.GONE);
            ll_price_range.setVisibility(View.GONE);
        } else {
            tv_price_range.setVisibility(View.VISIBLE);
            ll_price_range.setVisibility(View.VISIBLE);
        }
    }
    /**
     * 设置是否隐藏店铺服务选项
     */

    public void hideShopService(boolean isHidden){
        mShopServiceTitle.setVisibility(isHidden?View.GONE:View.VISIBLE);
        mShopServiceOptions.setVisibility(isHidden?View.GONE:View.VISIBLE);
    }

    /**
     * 设置是否隐藏药帮忙服务下的有促销选项
     *
     * @param isHidden
     */
    public void hiddenPromotion(boolean isHidden) {
        if (isHidden) {
            mTvPromotion.setVisibility(View.GONE);
        } else {
            mTvPromotion.setVisibility(View.VISIBLE);
        }
    }

    private void getResult() {

        setListContains(mList);

        String[] strings = setPriceRange();
        if ((TextUtils.isEmpty(strings[0]) && !TextUtils.isEmpty(strings[1])) ||
                (!TextUtils.isEmpty(strings[0]) && TextUtils.isEmpty(strings[1]))) {
            ToastUtils.showShort("请输入完整的价格区间");
            return;
        }

        if (listener != null) {
            SearchFilterBean filterBean = new SearchFilterBean(isAvailable, isPromotion, isClassA
                    , isClassB, isClassRx, isClassElse, strings[0], strings[1], new ArrayList<>(), isShunfeng, isJD, isCanUseCoupon, isDpby, isSpellGroupAndPgby, mNearEffective, mNearEffectiveStr, dynamicLabelResultMap);
            filterBean.specSelectedStr = getSelectedStr(mSelectedSpecList);
            filterBean.shopSelectedStr = getSelectedStr(mSelectedShopKeyList);
            filterBean.shopNameSelectedStr = getSelectedStr(mSelectedShopList);
            filterBean.lastNames.clear();
            filterBean.lastNames.addAll(mSelectedManufacturerList);
            filterBean.dynamicLabelConfig = mDynamicLabelConfigTemp;
            listener.onResult(filterBean);
            listener.onDismiss();
        }
        mLeftPopWindow.dismiss(true);
    }

    private String[] setPriceRange() {

        String[] strings = new String[2];

        String priceRangeFloor = mPriceRangeFloor.getText().toString().trim();
        String priceRangeTop = mPriceRangeTop.getText().toString().trim();

        try {
            if (!TextUtils.isEmpty(priceRangeFloor) && !TextUtils.isEmpty(priceRangeTop)) {
                int priceRangeFloorNum = Integer.parseInt(priceRangeFloor);
                int priceRangeTopNum = Integer.parseInt(priceRangeTop);
                String str = "";
                if (priceRangeFloorNum > priceRangeTopNum) {
                    str = priceRangeFloor;
                    priceRangeFloor = priceRangeTop;
                    priceRangeTop = str;
                }
            }
        } catch (NumberFormatException e) {
            priceRangeFloor = "";
            priceRangeTop = "";
        }
        mPriceRangeFloor.setText(priceRangeFloor);
        mPriceRangeTop.setText(priceRangeTop);
        strings[0] = priceRangeFloor;
        strings[1] = priceRangeTop;

        return strings;
    }

    public void setListener(LeftPopWindow.Listener<SearchFilterBean> listener) {
        if (mLeftPopWindow == null) {
            init();
        }
        this.listener = listener;
    }


    public void hideYBM() {
        if (mLeftPopWindow == null) {
            init();
        }
        if (tvYBM != null) {
            tvYBM.setVisibility(View.GONE);
        }
        if (llYBM != null) {
            llYBM.setVisibility(View.GONE);
        }
    }

    public void clearNearEffective() {
        sfnev.reset();
        mNearEffective = null;
    }

    public void reset(boolean isBrand) {

        //有效期
        sfnev.reset();
        mNearEffective = "";
        mNearEffectiveStr = "";

        //服务
        dynamicLabel.reset();
        int dynamicLabelExtCnt = lyDynamicLabelCnMedine.getChildCount();
        if(dynamicLabelExtCnt > 0){
            for (int i = 0; i<dynamicLabelExtCnt; i++){
                try{
                    SearchDynamicLabelExtView extView = (SearchDynamicLabelExtView)lyDynamicLabelCnMedine.getChildAt(i);
                    extView.reset();
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }
        dynamicLabelResultMap.clear();

        if (mManufacturersPop != null) {
            mManufacturersPop.reset(isBrand);
        }

        if (mList == null) {
            mList = new ArrayList<>();
        }
        mList.clear();

        if (isBrand) {
            key = "";
            value = "";
        }

        isAvailable = false;
        isPromotion = false;

        isClassA = false;
        isClassB = false;
        isClassRx = false;
        isClassElse = false;
        isCanUseCoupon = false;
        spec = "";
        shopCodes = "";
        mPriceRangeFloor.setText("");
        mPriceRangeTop.setText("");
        isJD = false;
        isShunfeng = false;
        resetSpecList();
        resetShopList();
        resetManufacturerList();

        handler.sendMessage(handler.obtainMessage(10, mList));
        handler.sendMessage(handler.obtainMessage(20));
        handler.sendMessage(handler.obtainMessage(30));
        handler.sendMessage(handler.obtainMessage(40));
        handler.sendMessage(handler.obtainMessage(50));
        handler.sendMessage(handler.obtainMessage(60));
        handler.sendMessage(handler.obtainMessage(70));
        handler.sendMessage(handler.obtainMessage(80)); //处理京东和顺丰快递选项
        handler.sendMessage(handler.obtainMessage(90)); //处理京东和顺丰快递选项

    }

    /**
     * 重置规格
     */
    private void resetSpecList() {
        mSelectedSpecList.clear();
        for (SearchFilterBean searchFilterBean : mSpecList) {
            searchFilterBean.isSelected = false;
        }
        setSpecList();
    }

    /**
     * 重置商家
     */
    private void resetShopList() {
        mSelectedShopList.clear();
        mSelectedShopKeyList.clear();
        for (SearchFilterBean searchFilterBean : mShopList) {
            searchFilterBean.isSelected = false;
        }
        setShopList();
    }

    /**
     * 重置生产厂家
     */
    private void resetManufacturerList() {
        mSelectedManufacturerList.clear();
        for (ManufacturersBean manufacturersBean : mManufacturerList) {
            manufacturersBean.isSelected = false;
        }
        setManufacturerList();
    }

    public void setNewData(List<String> list) {
        setProductTexts(list);
    }

    private void setProductTexts(List<String> list) {

        if (list == null || list.isEmpty()) {
            mLl01.setVisibility(View.GONE);
            mLl02.setVisibility(View.GONE);
            mLl03.setVisibility(View.GONE);
            mLl04.setVisibility(View.GONE);
            return;
        }
        //设置商品
        if (list.size() > 0 && !TextUtils.isEmpty(list.get(0))) {
            mLl01.setVisibility(View.VISIBLE);
            mLl02.setVisibility(View.GONE);
            mLl03.setVisibility(View.GONE);
            mLl04.setVisibility(View.GONE);
            mTvDetails01.setText(list.get(0));
        }
        if (list.size() > 1 && !TextUtils.isEmpty(list.get(1))) {
            mLl02.setVisibility(View.VISIBLE);
            mLl03.setVisibility(View.GONE);
            mLl04.setVisibility(View.GONE);
            mTvDetails02.setText(list.get(1));
        }
        if (list.size() > 2 && !TextUtils.isEmpty(list.get(2))) {
            mLl03.setVisibility(View.VISIBLE);
            mLl04.setVisibility(View.VISIBLE);
            mTvDetails03.setText(list.get(2));
        }

    }

    public void show() {
        if (mLeftPopWindow == null) {
            init();
        }
        if (mDynamicLabelExposure != null && mDynamicLabelConfig != null) {
            mDynamicLabelExposure.onExposure(mDynamicLabelConfig);
            mDynamicLabelConfigTemp = mDynamicLabelConfig;
            mDynamicLabelConfig = null;
        }
        sfnev.updateItemSelected(mNearEffective);
        //设置规格
        setSpecList();
        //设置店铺
        setShopList();
        //设置生产厂家
        setManufacturerList();
        mLeftPopWindow.show();
        mVm.updateSearchCurrentDynamicLabelSelected();
    }


    public void dismissPop() {
        if (mManufacturersPop != null) {
            mManufacturersPop.dismiss();
        }
        dismiss();
    }

    public void dismiss() {
        if (mLeftPopWindow != null) {
            mLeftPopWindow.dismiss(true);
        }
    }

    public boolean isShow() {
        if (mLeftPopWindow != null) {
            return mLeftPopWindow.isShow();
        }
        return false;
    }

    /*
     * 生产厂家
     * final int pos, final SearchFilterBean bean
     * */
    private void initManufacture() {
        if (mManufacturersPop == null) {
            if (!isPlan) {
                mManufacturersPop = new ManufacturersPop();
            } else {
                mManufacturersPop = new ManufacturersPop(planId);
            }
            mManufacturersPop.setListener(new LeftPopWindow.Listener<List<String>>() {
                @Override
                public void onDismiss() {

                }

                @Override
                public void onResult(List<String> list) {

                    setListContains(list);
                    handler.sendMessage(handler.obtainMessage(10, list));
                }
            });

        }
    }

    /**
     * 如果list包含“全部厂家”就把list制空
     *
     * @param list
     */
    private void setListContains(List<String> list) {
        if (list.contains(lastName)) {
            list.clear();
        }
    }

    @SuppressLint("HandlerLeak")
    private Handler handler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (msg.what == 10) {
                if (mList == null) {
                    mList = new ArrayList<>();
                }
                mList.clear();
                mList.addAll((List<String>) msg.obj);
                setNewData(mList);
            } else if (msg.what == 20) {
                mTvAvailable.setActivated(isAvailable);
            } else if (msg.what == 30) {
                mTvPromotion.setActivated(isPromotion);
            } else if (msg.what == 40) {
                mClassA.setActivated(isClassA);
            } else if (msg.what == 50) {
                mClassB.setActivated(isClassB);
            } else if (msg.what == 60) {
                mClassRx.setActivated(isClassRx);
            } else if (msg.what == 70) {
                mClassElse.setActivated(isClassElse);
            } else if (msg.what == 80) {
                mShopServiceJd.setActivated(isJD);
                mShopServiceShunfeng.setActivated(isShunfeng);
            } else if (msg.what == 90) {
                mCanUseCoupon.setActivated(isCanUseCoupon);
            } else if (msg.what == 100) {
                mTvDpby.setActivated(isDpby);
            } else if (msg.what == 110) {
                mTvDpby.setActivated(isDpby);
                mTvSpellGroupAndPgby.setActivated(isSpellGroupAndPgby);
            }
    }
};

    private boolean isScroll(MotionEvent ev) {
        if (minSlop <= 0) {
            minSlop = ViewConfiguration.get(BaseYBMApp.getAppContext()).getScaledTouchSlop();
        }
        LogUtils.d("action:" + ev.getAction());
        if (ev.getAction() != MotionEvent.ACTION_MOVE || lastAction != MotionEvent.ACTION_MOVE || (Math.abs(ev.getRawY() - lastY) < minSlop)) {
            lastAction = ev.getAction();
            lastY = ev.getRawY();
            return false;
        }
        lastAction = ev.getAction();
        lastY = ev.getRawY();
        LogUtils.d("滑动了啊----");
        hideSoftInput(mPriceRangeFloor, mPriceRangeTop);
        return true;
    }

    public void hideSoftInput(View view) {
        if (view == null) {
            return;
        }
        try {
            if (inputManager == null) {
                inputManager = (InputMethodManager) BaseYBMApp.getAppContext().getSystemService(Context.INPUT_METHOD_SERVICE);
            }
            inputManager.hideSoftInputFromWindow(view.getWindowToken(), 0);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }


    public void hideSoftInput(View... views) {
        for (View v : views) {
            hideSoftInput(v);
        }
    }

    /**
     * 展示有效期
     */
    public void showNearEffective() {
        sfnev.setVisibility(View.VISIBLE);
    }

    private IDynamicLabelExposure mDynamicLabelExposure;

    public void setDynamicLabelExposureAllListener(IDynamicLabelExposure iDynamicLabelExposure) {
        mDynamicLabelExposure = iDynamicLabelExposure;
    }

    public interface IDynamicLabelExposure {
        void onExposure(List<SearchDynamicLabelConfig> dynamicLabelConfig);
    }
}
