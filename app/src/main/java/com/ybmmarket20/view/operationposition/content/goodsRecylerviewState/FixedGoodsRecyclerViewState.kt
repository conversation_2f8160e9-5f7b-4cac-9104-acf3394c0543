package com.ybmmarket20.view.operationposition.content.goodsRecylerviewState

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybmmarket20.bean.OperationPositionInfo
import com.ybmmarket20.common.util.ConvertUtils

/**
 * 固定数量item
 */
class FixedGoodsRecyclerViewState(rv: RecyclerView, info: OperationPositionInfo) :
    GoodsRecyclerViewState(rv, info) {

    override fun initRecyclerView() {
        super.initRecyclerView()
        rv.layoutManager = GridLayoutManager(getContext(), 3)
    }

    override fun getNestedScrollingEnabled(): Boolean = false

    override fun addItemDivider() {
        super.addItemDivider()
        if (rv.itemDecorationCount > 0) {
            rv.removeItemDecorationAt(0)
        }
        rv.addItemDecoration(FixedCountDecoration())
    }

    inner class FixedCountDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            val dividerWidth = ConvertUtils.dp2px(10F)
            val position = parent.getChildLayoutPosition(view)
            when(position % 3) {
                0 -> dividerWidth to dividerWidth/2
                1 -> dividerWidth/2 to dividerWidth/2
                2 -> dividerWidth/2 to dividerWidth
                else -> 0 to 0
            }.apply {
                outRect.left = first
                outRect.right = second
            }
        }
    }
}