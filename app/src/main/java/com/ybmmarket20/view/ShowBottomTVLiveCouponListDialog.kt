package com.ybmmarket20.view

import android.content.Context
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextUtils
import android.text.style.AbsoluteSizeSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.bean.NetError
import com.ybm.app.view.CommonRecyclerView
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CouponBean
import com.ybmmarket20.bean.EmptyBean
import com.ybmmarket20.bean.TVLiveVoucherListBean
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.DialogUtil
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.StringUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import kotlinx.android.synthetic.main.layout_empty_view.view.*
import org.json.JSONObject
import java.util.*

class ShowBottomTVLiveCouponListDialog(ecLiveId: String?) : BaseBottomPopWindow() {
    private var mAdapter: YBMBaseAdapter<TVLiveVoucherListBean.CouponsBean>? = null
    private var mList: CommonRecyclerView? = null
    private var limit: Int = 30
    private var offset: Int = 0
    private var ecLiveId: String? = ecLiveId

    private var mDatas: MutableList<TVLiveVoucherListBean.CouponsBean> = ArrayList()

    override fun getLayoutId(): Int {
        return R.layout.layout_show_bottom_tv_live_coupon_list_dialog
    }

    override fun getLayoutParams(): LinearLayout.LayoutParams {
        // return LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, UiUtils.getScreenHeight() - ConvertUtils.dp2px(120f))
        var height = ConvertUtils.dp2px(450f)
        return LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, height)
    }


    override fun initView() {
        mList = getView(R.id.crv_list)

        contentView.findViewById<View>(R.id.product_detail_btn).setOnClickListener { dismiss() }

        mList?.setListener(object : CommonRecyclerView.Listener {
            override fun onRefresh() {
                getData(0)
            }

            override fun onLoadMore() {
                offset++
                getData(offset)
            }
        })
        mAdapter = object : YBMBaseAdapter<TVLiveVoucherListBean.CouponsBean>(R.layout.item_show_bottom_tv_live_coupon_list_dialog, mDatas) {
            override fun bindItemView(ybmBaseHolder: YBMBaseHolder, couponsBean: TVLiveVoucherListBean.CouponsBean) {
                val tvPrice = ybmBaseHolder.getView<TextView>(R.id.tv_price)
                val tvType = ybmBaseHolder.getView<TextView>(R.id.tv_type)
                val tvUseRange = ybmBaseHolder.getView<TextView>(R.id.tv_use_range)
                val tvPriceLimit = ybmBaseHolder.getView<TextView>(R.id.tv_price_limit)
                val tvGet = ybmBaseHolder.getView<TextView>(R.id.tv_get)
                val rlCoupon = ybmBaseHolder.getView<RelativeLayout>(R.id.rl_coupon)
                val ivIcon = ybmBaseHolder.getView<ImageView>(R.id.iv_icon)

                // 可领券
                var desc: SpannableStringBuilder
                if (couponsBean.voucherState == 1) {
                    val amount = UiUtils.transform2Int(couponsBean.discount)
                    desc = StringUtil.setDotAfterSize("${amount}折", 19)
                } else {
                    desc = SpannableStringBuilder("¥${UiUtils.transformInt(couponsBean.moneyInVoucher)}").apply {
                        setSpan(AbsoluteSizeSpan(ConvertUtils.dp2px(15f)), 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                    }
                }
                tvPrice.text = desc
                if (UiUtils.transformInt(couponsBean.moneyInVoucher).length > 3) {
                    tvPrice.textSize = ConvertUtils.dp2px(10f).toFloat()
                }


                tvType.text = couponsBean.voucherTypeDesc
                tvUseRange.text = couponsBean.voucherTitle

                // 最高减信息
                if (!couponsBean.maxMoneyInVoucherDesc.isNullOrEmpty()) {
                    tvPriceLimit.text = couponsBean.minMoneyToEnableDesc + " " + couponsBean.maxMoneyInVoucherDesc
                } else {
                    tvPriceLimit.text = couponsBean.minMoneyToEnableDesc
                }

                if (couponsBean.activityState == 5) {//已抢光
                    tvGet.visibility = View.GONE
                    rlCoupon.setBackgroundResource(R.drawable.icon_tv_live_coupon_list_dialog_no_bg)
                    ivIcon.setImageResource(R.drawable.icon_tv_live_coupon_no)
                } else if (couponsBean.activityState == 3 || couponsBean.activityState == 4) {//已领取
                    tvGet.visibility = View.GONE
                    rlCoupon.setBackgroundResource(R.drawable.icon_tv_live_coupon_list_dialog_have_yet_bg)
                    ivIcon.setImageResource(R.drawable.icon_tv_live_coupon_have_yet)
                } else {//立即领取
                    tvGet.visibility = View.VISIBLE
                    rlCoupon.setBackgroundResource(R.drawable.icon_tv_live_coupon_list_dialog_have_bg)
                    ivIcon.setImageDrawable(null)
                }

                tvGet.setOnClickListener {
                    //立即领取
                    if (mOnAvailableClickListener != null) {
                        mOnAvailableClickListener?.onAvailableClick(couponsBean)
                    }
                    if (couponsBean.activityState != 5 && couponsBean.activityState != 4 && couponsBean.activityState != 3) {
                        getCoupon(couponsBean, ybmBaseHolder.layoutPosition)
                    }

                }

                XyyIoUtil.track(XyyIoUtil.PAGE_WEBCAST_COUPON_EXPOSURE, JSONObject().apply {
                    put("webcastId", ecLiveId)
                    put("couponId", couponsBean?.templateId)
                    put("source", "1")  // 1.列表；2.卡片
                })

            }
        }
        val inflater = YBMAppLike.getAppContext().getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val emptyView = inflater.inflate(R.layout.layout_empty_view, null)
        emptyView.layoutParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
        emptyView.tv.text = "主播还没有上传优惠券"
        mAdapter?.emptyView = emptyView
        mList?.setAdapter(mAdapter)
        // mList?.setEmptyView(R.layout.layout_empty_view, R.drawable.icon_empty, "主播还没有上传商品")
        mAdapter?.openLoadMore(limit, true)
        mAdapter?.setShowNoData(false)
        mList?.setRefreshEnable(false)
        mList?.isEnabled = false
    }


    fun getParams(mOffset: Int): RequestParams {

        val merchantid = SpUtil.getMerchantid()
        val params = RequestParams()

        params.url = AppNetConfig.TV_LIVE_LIST_COUPONS
        params.put("merchantId", merchantid)
        params.put("ecLiveId", ecLiveId)
        params.put("offset", "" + mOffset)
        params.put("limit", "" + limit)
        return params
    }

    fun getData(mOffset: Int) {

        HttpManager.getInstance().post(getParams(mOffset), object : BaseResponse<TVLiveVoucherListBean>() {

            override fun onSuccess(content: String, obj: BaseBean<TVLiveVoucherListBean>?, data: TVLiveVoucherListBean) {
                if (obj != null && obj.isSuccess && data.coupons != null) {
                    if (mOffset == 0) {
                        mDatas.clear()
                    }
                    mDatas.addAll(data.coupons)
                    setNewData(mDatas)
                    mAdapter?.notifyDataChangedAfterLoadMore(data.pageNo < data.totalPage)
                }
            }

            override fun onFailure(error: NetError) {
                ToastUtils.showShort(error.message)
                mAdapter?.notifyDataChangedAfterLoadMore(false)
            }
        })
    }

    /**
     * 领取优惠券
     */
    fun getCoupon(mCouponBean: TVLiveVoucherListBean.CouponsBean, positon: Int) {
        val merchantid = SpUtil.getMerchantid()
        val params = RequestParams()
        params.put("merchantId", merchantid)
        params.put("ecLiveId", ecLiveId)
        params.put("voucherTemplateId", mCouponBean.templateId)
        params.url = AppNetConfig.TV_LIVE_GET_COUPON
        HttpManager.getInstance().post(params, object : BaseResponse<EmptyBean>() {

            override fun onSuccess(content: String, obj: BaseBean<EmptyBean>?, data: EmptyBean) {
                if (obj != null && obj.isSuccess) {
                    DialogUtil.showCommonStatus(CommonDialogLayout.CommonTip.success, "领取成功")
                    XyyIoUtil.track(XyyIoUtil.ACTION_WEBCAST_COUPON_GET, JSONObject().apply {
                        put("webcastId", ecLiveId)
                        put("couponId", mCouponBean.templateId)
                        put("source", "1")  // 1.列表；2.卡片
                    })
                    //dismiss()
                    // 领取成功后 局部刷新
                    mCouponBean.activityState = 3
                    mAdapter?.notifyItemChanged(positon)
                }
            }

            override fun onFailure(error: NetError) {
                ToastUtils.showShort(error.message)
            }
        })
    }

    override fun show(token: View?) {
        super.show(token)
        getData(0)
    }

    /**
     * @param list
     */
    fun setNewData(list: MutableList<TVLiveVoucherListBean.CouponsBean>) {
        this.mDatas = list
        if (mAdapter != null) {
            mAdapter?.setNewData(list)
        }
    }

    interface OnAvailableItemClickListener {
        fun onAvailableClick(coupon: TVLiveVoucherListBean.CouponsBean)
    }

    private var mOnAvailableClickListener: OnAvailableItemClickListener? = null

    fun setOnItemClickListener(listener: OnAvailableItemClickListener) {
        this.mOnAvailableClickListener = listener
    }
}