package com.ybmmarket20.view;

import android.content.Context;
import com.google.android.material.appbar.AppBarLayout;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import com.ybm.app.view.refresh.RecyclerRefreshLayout;

/**
 * 增加头部下拉刷新
 */
public class RefreshViewBehavior extends AppBarLayout.ScrollingViewBehavior {
    private AppBarLayout target;
    private RecyclerRefreshLayout child;
    private int targetHei;
    private int targetY;
    private float y;
    private float x;

    public RefreshViewBehavior(Context context, AttributeSet attrs) {
        super(context, attrs);
    }


    @Override
    public boolean layoutDependsOn(CoordinatorLayout parent, View child, View dependency) {
        if (target == null) {
            target = (AppBarLayout) parent.getChildAt(0);
        }
        if (this.child == null) {
            this.child = (RecyclerRefreshLayout) child;
        }
        targetHei = target.getMeasuredHeight();
        targetY = targetHei;
        return super.layoutDependsOn(parent, child, dependency);
    }

    @Override
    public boolean onInterceptTouchEvent(CoordinatorLayout parent, View child, MotionEvent ev) {
        if (!child.isEnabled()) {
            return super.onInterceptTouchEvent(parent, child, ev);
        }
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                y = ev.getRawY();
                x = ev.getRawX();
                if (ev.getY() > targetHei) {
                    return super.onInterceptTouchEvent(parent, child, ev);
                }
                break;
            case MotionEvent.ACTION_MOVE:
                float diffY = ev.getRawY() - y;
                float diffX = ev.getRawX() - x;
                if (y < targetY && diffY > 2 && diffY > Math.abs(diffX)) {//转发向下滑动手势
                    return true;
                }
                break;
        }
        return super.onInterceptTouchEvent(parent, child, ev);
    }

    @Override
    public boolean onTouchEvent(CoordinatorLayout parent, View child, MotionEvent ev) {
        if (!child.isEnabled()) {
            return super.onTouchEvent(parent, child, ev);
        }
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                break;
            case MotionEvent.ACTION_MOVE:
                float diffY = ev.getRawY() - y;
                if (diffY > 0) {
                    this.child.moveSpinner(diffY);
                    return true;
                }
                break;
            case MotionEvent.ACTION_CANCEL:
            case MotionEvent.ACTION_UP:
                try {
                    this.child.finishSpinner();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                break;
        }
        return super.onTouchEvent(parent, child, ev);
    }
}
