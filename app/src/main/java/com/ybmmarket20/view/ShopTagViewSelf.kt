package com.ybmmarket20.view

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.ybmmarket20.R
import com.ybmmarket20.common.util.ConvertUtils

class ShopTagViewSelf : ShopTagView {
    constructor(context: Context?) : super(context)
    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs)

    override fun createView(bean: String?): TextView {
        return TextView(context).apply {
            LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)
                .also {
                    it.setMargins(0, 0, ConvertUtils.dp2px(5f), 0)
                }.also(this::setLayoutParams)
            gravity = Gravity.CENTER
            setSingleLine(true)
            setPadding(ConvertUtils.dp2px(3f), 2, ConvertUtils.dp2px(3f), 2)
            setTextSize(TypedValue.COMPLEX_UNIT_SP, 10f)
            setBackgroundResource(R.drawable.shape_goods_detail_shop_portal_tag)
            setTextColor(ContextCompat.getColor(context, R.color.color_7a5421))
            includeFontPadding = false
            text = bean?: ""

        }
    }
}