package com.ybmmarket20.view.homesteady

import android.content.Context
import androidx.recyclerview.widget.LinearLayoutManager
import android.util.AttributeSet
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.bean.homesteady.HostSearchItem
import kotlinx.android.synthetic.main.layout_home_steady_recommandkey.view.*

/**
 * <AUTHOR>
 * @date 2020-05-07
 * @description 首页推荐热词view
 */
class HomeSteadyRecommendHotKeyView(context: Context, attr: AttributeSet): BaseHomeSteadyView(context, attr) {

    val mData: MutableList<HostSearchItem> = mutableListOf()
    var adapter: HomeSteadyRecommendHostKeyAdapter? = null

    override fun getLayoutId(): Int = R.layout.layout_home_steady_recommandkey

    override fun initPlaceHold() {
        super.initPlaceHold()
        setAdapterData(generatePlaceHoldData())
    }

    /**
     * 设置推荐数据
     */
    fun setRecommendSearchKeyData(data: MutableList<HostSearchItem>?) = data?.also(this::setAdapterData)

    /**
     * 设置列表数据
     */
    private fun setAdapterData(data: MutableList<HostSearchItem>) {
        mData.clear()
        mData.addAll(data)
        if (adapter == null) {
            adapter = HomeSteadyRecommendHostKeyAdapter(context, mData)
            rc_recommend.layoutManager = WrapLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            rc_recommend.setAdapter(adapter)
        } else {
            adapter?.notifyDataSetChanged()
        }
    }

    /**
     * 生成占位数据
     */
    private fun generatePlaceHoldData(): MutableList<HostSearchItem> = arrayListOf<HostSearchItem>().apply {
        for(item in 0 until 10) {
            add(HostSearchItem().apply {
                itemType = HOME_STEADY_LAYOUT_DEFAULT
            })
        }
    }

}