package com.ybmmarket20.adapter;

import android.view.View;
import android.widget.TextView;

import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.RequestSupplierBean;

import java.util.List;

/**
 * Created by xyy on 2018/10/18.
 */

public class MySupplierAdapter extends YBMBaseAdapter<RequestSupplierBean.SupplierBean> {

    private OnActionCallback callback;

    public MySupplierAdapter(int layoutResId, List<RequestSupplierBean.SupplierBean> data, OnActionCallback callback) {
        super(layoutResId, data);
        this.callback = callback;
    }

    @Override
    protected void bindItemView(YBMBaseHolder ybmBaseHolder, RequestSupplierBean.SupplierBean supplierBean) {
        View layout_look = ybmBaseHolder.getView(R.id.ll_look_info);
        TextView tv_into = ybmBaseHolder.getView(R.id.tv_into);
        TextView tv_supplier_name = ybmBaseHolder.getView(R.id.tv_supplier_name);
        tv_supplier_name.setText(supplierBean.getCompanyName());
        ybmBaseHolder.setImageUrl(R.id.iv_supplier_logo, supplierBean.getLogoUrl());

        //查看资质
        layout_look.setOnClickListener(v ->{
            if (callback != null) {
                callback.onActionLookMore(supplierBean);
            }
        });

        //进店
        tv_into.setOnClickListener(v ->{
            if (callback != null) {
                callback.onActionIntoShop(supplierBean);
            }
        });
    }

    public interface OnActionCallback{
        void onActionLookMore(RequestSupplierBean.SupplierBean supplierBean);
        void onActionIntoShop(RequestSupplierBean.SupplierBean supplierBean);
    }
}
