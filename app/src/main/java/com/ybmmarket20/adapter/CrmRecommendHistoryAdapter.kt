package com.ybmmarket20.adapter

import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.widget.TextView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.RecommendHistoryBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.constant.ROUTER_CRM_RECOMMEND
import com.ybmmarket20.utils.RoutersUtils
import java.text.SimpleDateFormat
import java.util.*

/**
 * <AUTHOR> Brin
 * @date : 2020/6/30 - 17:43
 * @Description :
 * @version
 */
class CrmRecommendHistoryAdapter(layoutId: Int, rowsWrapper: List<RecommendHistoryBean>) : YBMBaseAdapter<RecommendHistoryBean>(layoutId, rowsWrapper) {

    private var pool: RecyclerView.RecycledViewPool? = null
    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: RecommendHistoryBean?) {
        baseViewHolder?.getView<TextView>(R.id.tv_recommend_time)?.setText("推荐时间: ${SimpleDateFormat("yyyy-MM-dd HH:mm").format(Date(t?.recommendDate ?: -1))}")
        val horizontalAdapter: ProductGrid3AdapterCms = ProductGrid3AdapterCms(mutableListOf<RowsBean>())
        baseViewHolder?.getView<RecyclerView>(R.id.rv)
                ?.apply { if (baseViewHolder.position == 0) pool = recycledViewPool }
                ?.let {
                    horizontalAdapter.setNewData(t?.productDTOList?.take(3))
                    it.layoutManager = GridLayoutManager(mContext, 3)
                    it.adapter = horizontalAdapter
                    horizontalAdapter.setOnListItemClickListener { RoutersUtils.open("ybmpage://productdetail/" + it.getId()) }
                }
        baseViewHolder?.itemView?.setOnClickListener { RoutersUtils.open("$ROUTER_CRM_RECOMMEND/${t?.recommendDate}") }
//        baseViewHolder?.getView<View>(R.id.touch_overlay)?.setOnClickListener { RoutersUtils.open("$ROUTER_CRM_RECOMMEND/${t?.recommendDate}") }
    }

}