package com.ybmmarket20.adapter

import android.graphics.Typeface
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StrikethroughSpan
import android.text.style.StyleSpan
import android.view.View
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LiveData
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.ProductEditLayoutSuccessParams
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SpellGroupGoodsItem
import com.ybmmarket20.bean.SpellGroupRecommendGoodsBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.view.ProductEditLayoutNew
import com.ybmmarket20.view.ProductEditLayoutSuiXinPin
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarketkotlin.utils.TextWithPrefixTag
import kotlin.math.absoluteValue

class SpellGroupRecommendSelectedGoodsCartsAdapter(
    val spellGroupRecommendGoodsLiveData: LiveData<SpellGroupRecommendGoodsBean>?,
    val addCartCallback: AddCartCallback
) :
    YBMBaseAdapter<SpellGroupGoodsItem>(
        R.layout.item_spell_group_recommend_goods_carts,
        spellGroupRecommendGoodsLiveData?.value?.rowsBean?.filter {
            spellGroupRecommendGoodsLiveData.value!!.goodsIdMapping[it.skuId + ""] != 0
        }
    ) {
    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: SpellGroupGoodsItem?) {
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            ImageUtil.load(
                mContext,
                AppNetConfig.LORD_IMAGE + bean.goodsUrl,
                holder.getView(R.id.iv_goods)
            )
            ImageUtil.loadNoPlace(
                mContext,
                AppNetConfig.LORD_TAG + bean.goodsTagUrl,
                holder.getView(R.id.iv_goods_tag)
            )
            holder.setText(R.id.tv_goods_title, bean.goodsTitle)
            try {
                holder.setVisible(R.id.tv_effect, !bean.nearEffect.isNullOrEmpty())
                holder.setText(R.id.tv_effect, "有效期：${bean.nearEffect}")
            } catch (e: Exception) {
                e.printStackTrace()
            }

            //设置价格样式
            val priceBuilder = getPriceSpannableBuilder(UiUtils.transform(bean.goodsPrice),UiUtils.transform(bean.goodsOriginalPrice))
            holder.setText(R.id.tv_price, priceBuilder)
            //加购
            val pel = holder.getView<ProductEditLayoutSuiXinPin>(R.id.pel)
            pel.bindData(
                bean.skuId ?: "",
                1,
                true,
                true,
                1,
                bean.isSplit == 1,
                "${spellGroupRecommendGoodsLiveData?.value?.goodsIdMapping?.get(bean.skuId) ?: 0}"
            )
            pel.setOnAddCartListener(object : ProductEditLayoutSuiXinPin.AddCartListener {
                override fun onPreAddCart(params: RequestParams?): RequestParams = RequestParams()

                override fun onAddCartSuccess(params: ProductEditLayoutSuccessParams?) {
                    val goodsAmount =
                        ((params?.amount?.toIntOrNull() ?: 0) - (params?.preAmount ?: 0))
                    addCartCallback.addCart(
                        bean,
                        goodsAmount > 0,
                        goodsAmount.absoluteValue
                    )
                }
            })
        }
    }

    private fun getPriceSpannableBuilder(
        productPrice: String,
        fobPrice: String
    ): SpannableStringBuilder {
        val priceBuilder = UiUtils.getPriceWithFormat("¥${productPrice}  ", 14)
        priceBuilder.setSpan(
            StyleSpan(Typeface.BOLD),
            0,
            priceBuilder.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        //原价
        val fob = "¥${fobPrice}"

        if (!UiUtils.transform(fobPrice).equals(UiUtils.transform(productPrice))) {
            val originalPriceBuilder = SpannableStringBuilder(fob)
            originalPriceBuilder.setSpan(
                AbsoluteSizeSpan(11, true),
                0,
                originalPriceBuilder.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            originalPriceBuilder.setSpan(
                ForegroundColorSpan(
                    ContextCompat.getColor(
                        mContext,
                        R.color.color_676773
                    )
                ), 0, originalPriceBuilder.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            originalPriceBuilder.setSpan(
                StrikethroughSpan(),
                0,
                originalPriceBuilder.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            priceBuilder.append(originalPriceBuilder)
        }
        return priceBuilder
    }

}
