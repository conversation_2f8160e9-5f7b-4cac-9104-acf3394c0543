package com.ybmmarket20.adapter;

import android.text.TextUtils;
import android.view.View;

import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.GiftSkulistBean;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.GiftView;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class MineVipGiftAdapter extends YBMBaseAdapter<GiftSkulistBean> {

    private SimpleDateFormat dateFormat;

    public MineVipGiftAdapter(int layoutResId, List<GiftSkulistBean> data) {
        super(layoutResId, data);
        dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());

    }

    @Override
    protected void bindItemView(YBMBaseHolder ybmBaseHolder, final GiftSkulistBean rowsBean) {

        //绑定数据
        GiftView gf = ybmBaseHolder.getView(R.id.gf);
        if (rowsBean.giftSkulist != null && rowsBean.giftSkulist.size() > 0) {
            gf.bindData(rowsBean.giftSkulist, rowsBean.giftSkulist.size(), rowsBean.status);
        } else {
            gf.setVisibility(View.INVISIBLE);
        }

        //时间
        String startTime = dateFormat.format(new Date(rowsBean.startTime));
        String endTime = dateFormat.format(new Date(rowsBean.endTime));

        String amout = "";
        if (rowsBean.useAmount > 0.00) {
            amout = "满" + UiUtils.transform(rowsBean.useAmount);
        }

        String text = "";
        if (rowsBean.isStatus()) {
            text = startTime + "至" + endTime + "时间段内下单" + amout + "即可获赠该大礼包，随订单一并发货";
        } else {
            text = "有效期：" + startTime + "至" + endTime;
        }
        ybmBaseHolder.setText(R.id.tv_time, text)
                .setText(R.id.btn_ok, getStatus(rowsBean.status))
                .setBackgroundRes(R.id.ll_gift_item_bg_01, rowsBean.isStatus()
                        ? R.drawable.icon_gift_item_bg_01 : R.drawable.icon_gift_item_bg_02)
                .setImageResource(R.id.iv_gift_item_bg_05, rowsBean.isStatus()
                        ? R.drawable.icon_gift_item_bg_07 : R.drawable.icon_gift_item_bg_08)
                .setBackgroundRes(R.id.btn_ok, rowsBean.isStatus()
                        ? R.drawable.bg_vip_gift : R.drawable.bg_vip_gift_02)
                .setBackgroundRes(R.id.ll_gift_item_bg_04, rowsBean.isStatus()
                        ? R.drawable.icon_gift_item_bg_03 : R.drawable.icon_gift_item_bg_04)
                .setBackgroundRes(R.id.ll_gift_item_bg_05, rowsBean.isStatus()
                        ? R.drawable.icon_gift_item_bg_05 : R.drawable.icon_gift_item_bg_06)
                .setTextColor(R.id.tv_time, UiUtils.getColor(rowsBean.isStatus()
                        ? R.color.text_292933 : R.color.text_9494A6))
                .setOnClickListener(R.id.btn_ok, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        String action = "ybmpage://main/0";
                        if (!TextUtils.isEmpty(rowsBean.appUrl)) {
                            action = rowsBean.appUrl;
                        }
                        RoutersUtils.open(action);
                    }
                })
                .getView(R.id.btn_ok).setEnabled(rowsBean.isStatus());

    }

    /*
     * 设置文案
     * */
    public String getStatus(int status) {

        String statusStr = "";
        switch (status) {
            case 1:
            default:
                statusStr = "去下单";
                break;
            case 2:
                statusStr = "已领取";
                break;
            case 3:
                statusStr = "已过期";
                break;
            case 4:
                statusStr = "已拒绝";
                break;
        }
        return statusStr;
    }
}
