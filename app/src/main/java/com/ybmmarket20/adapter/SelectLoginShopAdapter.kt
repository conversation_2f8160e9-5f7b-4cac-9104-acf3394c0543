package com.ybmmarket20.adapter

import android.widget.TextView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.SelectLoginShopInfo
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarketkotlin.utils.textWithSuffixTag

/**
 * 选择登录店铺
 */
class SelectLoginShopAdapter(data: MutableList<SelectLoginShopInfo>) :
    YBMBaseAdapter<SelectLoginShopInfo>(R.layout.item_select_login_shop, data) {

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: SelectLoginShopInfo?) {
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            val shopName = holder.getView<TextView>(R.id.tvShopName)
            shopName.textWithSuffixTag(listOf(bean.tagBean), bean.name, 1)
            holder.setText(R.id.tvShopAddress, "${bean.province}${bean.city}${bean.district}${bean.street}${bean.address}")
//            holder.itemView.setOnClickListener {
//                RoutersUtils.open(bean.routerUrl)
//            }
        }
    }

}