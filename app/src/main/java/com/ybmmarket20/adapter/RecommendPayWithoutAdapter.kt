package com.ybmmarket20.adapter

import android.graphics.Typeface
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.PaymentSuiXinPinSkusItemBean
import com.ybmmarket20.bean.ProductEditLayoutSuccessParams
import com.ybmmarket20.bean.RecommendShunShouMaiBean
import com.ybmmarket20.bean.SpellGroupGoodsItem
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.view.ProductEditLayoutSuiXinPin
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.viewmodel.SpellGroupRecommendGoodsViewModel
import com.ybmmarket20.xyyreport.page.payment.suixinpin.SuiXinPinSSM
import com.ybmmarketkotlin.utils.TextWithPrefixTag
import kotlin.math.absoluteValue

//顺手买弹窗
class RecommendPayWithoutAdapter(
    goodsList: List<PaymentSuiXinPinSkusItemBean>,
    val onAddCartListener: (RecommendShunShouMaiBean) -> Unit,
    val mViewModel: SpellGroupRecommendGoodsViewModel
) : PaymentSuiXinPinAnalysisPopWindowAdapter<PaymentSuiXinPinSkusItemBean>(
    R.layout.item_spell_group_recommend_goods,
    goodsList,
    SuiXinPinSSM
) {
    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: PaymentSuiXinPinSkusItemBean) {
        super.bindItemView(baseViewHolder, t)
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            ImageUtil.load(
                mContext,
                AppNetConfig.LORD_IMAGE + bean.imageUrl,
                holder.getView(R.id.iv_goods)
            )
//            holder.setText(R.id.tv_goods_title, bean.showName)
            val goodsTitle = holder.getView<TextView>(R.id.tv_goods_title)
            goodsTitle.TextWithPrefixTag(bean.tagList, bean.showName)
            goodsTitle.setLineSpacing(0f, 1.1f)
            holder.setText(R.id.tv_effect, "有效期：" + bean.nearEffect)
            //设置价格样式
            var price = ""
            if (TextUtils.isEmpty(bean.price)) {
                price = bean.fob ?: ""
            } else {
                price = bean.price ?: ""
            }
            val priceBuilder =
                UiUtils.getPriceWithFormat(
                    "¥${UiUtils.transform(price)}",
                    11
                )
            priceBuilder.setSpan(
                StyleSpan(Typeface.BOLD),
                0,
                priceBuilder.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            val unitBuilder = SpannableStringBuilder("/${bean.productUnit} ")
            unitBuilder.setSpan(
                ForegroundColorSpan(
                    ContextCompat.getColor(
                        mContext,
                        R.color.color_676773
                    )
                ), 0, unitBuilder.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            unitBuilder.setSpan(
                AbsoluteSizeSpan(11, true),
                0,
                unitBuilder.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            priceBuilder.append(unitBuilder)
//            if (bean.fob.toString().isNotEmpty() && !UiUtils.transform(bean.fob)
//                    .equals(UiUtils.transform(price))
//            ) {
//                val originalPriceBuilder =
//                    SpannableStringBuilder("¥${bean.fob ?: ""}")
//                originalPriceBuilder.setSpan(
//                    AbsoluteSizeSpan(11, true),
//                    0,
//                    originalPriceBuilder.length,
//                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
//                )
//                originalPriceBuilder.setSpan(
//                    ForegroundColorSpan(
//                        ContextCompat.getColor(
//                            mContext,
//                            R.color.color_676773
//                        )
//                    ), 0, originalPriceBuilder.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
//                )
//                originalPriceBuilder.setSpan(
//                    StrikethroughSpan(),
//                    0,
//                    originalPriceBuilder.length,
//                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
//                )
//                priceBuilder.append(originalPriceBuilder)
//            }
            holder.setText(R.id.tv_price, priceBuilder)
            //加购
            val pel = holder.getView<ProductEditLayoutSuiXinPin>(R.id.pel)
            pel.bindData(
                "${bean.id}",
                1,
                true,
                true,
                bean.mediumPackageNum ?: 1,
                bean.isSplit == 1,
                "${mViewModel.spellGroupWithoutGoodsLiveData.value?.goodsIdMapping?.get(bean.id) ?: 0}"
            )
            pel.setOnAddCartListener(object : ProductEditLayoutSuiXinPin.AddCartListener {
                override fun onPreAddCart(params: RequestParams?): RequestParams =
                    RequestParams()

                override fun onAddCartSuccess(params: ProductEditLayoutSuccessParams?) {
                    if (baseViewHolder?.itemView?.context is BaseActivity) {
                        (baseViewHolder.itemView.context as BaseActivity).hideSoftInput()
                    }
                    val spellGroupGoodsItem = SpellGroupGoodsItem(
                        bean.imageUrl,
                        bean.markerUrl,
                        bean.showName,
                        price ?: "0",
                        bean.productUnit,
                        "${bean.fob}",
                        0,
                        bean.id,
                        1,
                        bean.isSplit,
                        nearEffect = bean.nearEffect
                    )
                    spellGroupGoodsItem.qtData = bean.qtData
                    var amount = 0
                    try {
                        amount = params?.amount?.toInt() ?: 0
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                    var skuStartNum = bean.actPt?.skuStartNum ?: bean.actPgby?.skuStartNum ?: 1
                    //如果第一次点击，并且加购数量不等于中包装数量，则把数量设置为起拼数量
                    if (params?.preAmount == 0 && amount != skuStartNum) {
                        amount = skuStartNum
                        pel.bindData(
                            bean.id ?: "",
                            1,
                            true,
                            true,
                            bean.mediumPackageNum ?: 1,
                            bean.isSplit == 1,
                            "${skuStartNum}"
                        )
                    }
                    //如果是减购，当前商品数量小于起拼数，把数量置为0
                    if (params?.addType == ProductEditLayoutSuiXinPin.BUTTON_TYPE_REDUCE && amount < skuStartNum) {
                        amount = 0
                        pel.bindData(
                            bean.id ?: "",
                            1,
                            true,
                            true,
                            bean.mediumPackageNum ?: 1,
                            bean.isSplit == 1,
                            "0"
                        )
                    }
                    val goodsAmount =
                        (amount - (params?.preAmount ?: 0))

                    if (goodsAmount > 0) {
                        (mContext as? BaseActivity)?.showProgress()
                        mViewModel.getChangeCartForPromotion(
                            bean.id ?: "",
                            amount, spellGroupGoodsItem,
                            goodsAmount > 0,
                            goodsAmount.absoluteValue
                        )
                    } else {
                        mViewModel.addWithoutShopCart(
                            spellGroupGoodsItem,
//                            (params?.addType ?: "") == ProductEditLayoutNew.BUTTON_TYPE_ADD,
                            goodsAmount > 0,
                            goodsAmount.absoluteValue
                        )
                    }
                    onAddCartListener.invoke(
                        RecommendShunShouMaiBean(
                            bean.id.toString(), price,
                            amount
                        )
                    )
                }
            })
        }
    }
}