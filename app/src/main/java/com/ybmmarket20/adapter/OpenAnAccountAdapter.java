package com.ybmmarket20.adapter;

import android.widget.TextView;

import com.ybm.app.adapter.YBMBaseHolder;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.ClientResourcesList;

import java.util.List;

/*
 * pop-开户
 * */
public class OpenAnAccountAdapter extends YBMGroupListAdapter<ClientResourcesList> {

    public OpenAnAccountAdapter(List<ClientResourcesList> data) {
        super(R.layout.open_on_account_item_group, R.layout.open_on_account_item_content, data);
    }

    @Override
    public void bindGroupView(YBMBaseHolder ybmBaseHolder, ClientResourcesList bean) {
        ybmBaseHolder.setText(R.id.tv_group, bean.resourceName);
        ((TextView) ybmBaseHolder.getView(R.id.tv_group)).setCompoundDrawablesWithIntrinsicBounds(null, null, mContext.getResources().getDrawable(bean.isExpanded() ? R.drawable.icon_up_arrow : R.drawable.icon_down_arrow), null);
        if (getData().indexOf(bean) == getData().size() - 1) {
            ybmBaseHolder.itemView.setBackgroundResource(R.drawable.shape_shop_qualification);
        } else {
            ybmBaseHolder.itemView.setBackgroundResource(R.drawable.shape_shop_qualification_normal);
        }
        ybmBaseHolder.setOnClickListener(R.id.tv_group, v -> {
            //是否一条物流信息下有多条数据
            if (bean.getSubItems() != null && bean.getSubItems().size() > 0) {
                //展开状态
                if (bean.isExpanded()) {
                    collapse_(getData().indexOf(bean));
                } else {
                    expand_(getData().indexOf(bean));
                }
            }
        });

    }

    @Override
    public void bindContentView(YBMBaseHolder ybmBaseHolder, ClientResourcesList bean) {
        ybmBaseHolder.setText(R.id.tv_content, bean.resourceDescription);

    }
}
