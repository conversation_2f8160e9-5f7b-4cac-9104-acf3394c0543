package com.ybmmarket20.network.request

import com.ybmmarket20.bean.*
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import com.ybmmarket20.utils.SpUtil
import com.ybmmarketkotlin.bean.ApplyNoticeBean
import com.ybmmarketkotlin.bean.CommodityGroupRecommondBean
import com.ybmmarketkotlin.bean.SameSpecificationsListResponse
import retrofit2.http.Field
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query
import java.lang.Exception

interface ComodityService {

    @FormUrlEncoded
    @POST("sku/listAlikeGroupBuyingProducts")
    suspend fun getGroupRecommendProducts(@Field("productId") productId: String): BaseBean<CommodityGroupRecommondBean>?

    @GET("sku/findSpuAggregatSku")
    suspend fun getSameSpecificationsList(@Query("id") productId: String, @Query("merchantId") merchantId: String): BaseBean<SameSpecificationsListResponse>?

    @FormUrlEncoded
    @POST("search/v2/searchRecPurchase")
    suspend fun getCombinedProducts(@Field("mainCsuId") productId: String,@Field("showRecPurchaseType") showRecPurchaseType: Int): BaseBean<ProductDetailCombinedBean>?
}

class ComodityRequest {

    /**
     *  获取支付成功页面头部信息
     */
    suspend fun getGroupRecommendProducts(productId: String): BaseBean<CommodityGroupRecommondBean>? = try {
        NetworkService.instance.mRetrofit.create(ComodityService::class.java).getGroupRecommendProducts(productId)
    } catch (e: Exception) {
        BaseBean<CommodityGroupRecommondBean>().initWithException(e)
    }

    suspend fun getSameSpecificationsList(productId: String): BaseBean<SameSpecificationsListResponse>? = try {

        NetworkService.instance.mRetrofit.create(ComodityService::class.java).getSameSpecificationsList(productId, SpUtil.getMerchantid())
    } catch (e: Exception) {
        BaseBean<SameSpecificationsListResponse>().initWithException(e)
    }

    /**
     * @desc    组合购、加价购 通过主品获取副品信息
     * [showRecPurchaseType] 1:组合购 2：加价购
     */
    suspend fun getCombinedProducts(productId: String,showRecPurchaseType:Int): BaseBean<ProductDetailCombinedBean>? = try {
        NetworkService.instance.mRetrofit.create(ComodityService::class.java).getCombinedProducts(productId, showRecPurchaseType)
    } catch (e: Exception) {
        BaseBean<ProductDetailCombinedBean>().initWithException(e)
    }

}



