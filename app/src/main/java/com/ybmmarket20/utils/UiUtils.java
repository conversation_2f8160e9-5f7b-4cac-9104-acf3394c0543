package com.ybmmarket20.utils;

import android.annotation.TargetApi;
import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.Point;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.media.MediaMetadataRetriever;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.google.android.material.tabs.TabLayout;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.InputFilter;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.view.Display;
import android.view.TouchDelegate;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.apkfuns.logutils.LogUtils;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.util.Abase;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.constant.AppNetConfig;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.ybmmarket20.adapter.AptitudeLogAdapter.STATUS_PASS;
import static com.ybmmarket20.adapter.AptitudeLogAdapter.STATUS_REJECT;
import static com.ybmmarket20.bean.LicenseListBean.License.STATUS_AWAITS_RECYCLING;
import static com.ybmmarket20.bean.LicenseListBean.License.STATUS_CANCELLATION;
import static com.ybmmarket20.bean.LicenseListBean.License.STATUS_DISQUALIFICATION;
import static com.ybmmarket20.bean.LicenseListBean.License.STATUS_FIRST_APTITUDE_NO_RECYCLE;
import static com.ybmmarket20.bean.LicenseListBean.License.STATUS_PAST_DUE;
import static com.ybmmarket20.bean.LicenseListBean.License.STATUS_RECYCLED;
import static com.ybmmarket20.bean.LicenseListBean.License.STATUS_REJECTED;
import static com.ybmmarket20.bean.LicenseListBean.License.STATUS_STEP1;
import static com.ybmmarket20.bean.LicenseListBean.License.STATUS_STEP2;
import static com.ybmmarket20.bean.LicenseListBean.License.STATUS_UNCOMMITTED;


/**
 * Created by asus on 2016/3/10.
 */
public class UiUtils extends com.ybm.app.utils.UiUtils {

    public static void toast(int resId) {
        if (resId <= 0) {
            return;
        }
        toast(getContext().getString(resId), false);
    }

    /**
     * 保证任务是运行在主线程当中的
     *
     * @param runnable
     */
    public static void runOnMainThread(Runnable runnable) {
        //当前线程id
        if (Looper.myLooper() == Looper.getMainLooper()) {
            runnable.run();
        } else {
            getHandler().post(runnable);
        }
    }

    /*
     *  入库价计算
     * */
    public static String getOrderPriceNumber(String num) {

        try {
            double v = Double.parseDouble(num);
            if (v >= 0.01) {
                BigDecimal bigDecimal = new BigDecimal(num);
                // 保留两位小数
                DecimalFormat formater = new DecimalFormat("0.00");
                // 四舍五入
                formater.setRoundingMode(RoundingMode.HALF_UP);
                return formater.format(bigDecimal);
            } else {
                return num;
            }
        } catch (Exception e) {
            return num;
        }

    }

    /*
     *
     * 转换为万
     * */
    public static String getNumber(String saleSkuNum) {

        try {
            double v = Double.parseDouble(saleSkuNum);
            if (v >= 10000) {
                BigDecimal bigDecimal = new BigDecimal(saleSkuNum);
                // 转换为万（除以10000）
                BigDecimal decimal = bigDecimal.divide(new BigDecimal("10000"));
                // 保留两位小数
                DecimalFormat formater = new DecimalFormat("0.00");
                // 四舍五入
                formater.setRoundingMode(RoundingMode.HALF_UP);
                // 格式化完成之后得出结果
                String formatNum = formater.format(decimal);
                formatNum = formatNum + "万";
                return formatNum;
            } else {
                return saleSkuNum;
            }
        } catch (Exception e) {
            return saleSkuNum;
        }

    }

    /**
     * 数字保留两位小数
     *
     * @return
     */
    public static String transform(double o) {
        try {
            return String.format("%.2f", o);
        } catch (Throwable e) {
            e.printStackTrace();
            LogUtils.d(e);
        }
        return "0.00";
    }

    /**
     * 数字保留两位小数
     *
     * @return
     */
    public static String transform(String str) {
        try {
            return transform(Double.parseDouble(str));
        } catch (Throwable e) {
            e.printStackTrace();
            LogUtils.d(e);
        }
        return "0.00";
    }

    /**
     * 数字保留两位小数
     *
     * @return
     */
    public static String transform(float o) {
        try {
            return String.format("%.2f", o);
        } catch (Throwable e) {
            e.printStackTrace();
            LogUtils.d(e);
        }
        return "0.00";
    }

    /**
     * 整数
     *
     * @return
     */
    public static String transformInt(Object o) {

        String format = String.format("%.0f", o);
        return format;
    }

    /**
     * 数字一位小数
     *
     * @return
     */
    public static String transform2Int(double o) {
        try {
            return String.format("%.1f", o);
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return "0.0";
    }

    /**
     * 如果指定的数与参数相等返回0。
     * 如果指定的数小于参数返回 -1。
     * 如果指定的数大于参数返回 1。
     *
     * @param oldP
     * @param newP
     * @return
     */
    public static boolean BigDecimalPrice(String oldP, String newP) {

        try {
            BigDecimal data1 = new BigDecimal(oldP);
            BigDecimal data2 = new BigDecimal(newP);
            int IsPrice = data2.compareTo(data1);
            return IsPrice == 1 || IsPrice == 0;
        } catch (Exception e) {
            return true;
        }
    }

    /**
     * 判断字符串是否是0
     *
     * @return
     */
    public static boolean BigDecimalPriceTo0(String p) {
        try {
            float parse = Float.parseFloat(p);
            return parse == 0;
        } catch (Exception e) {
            return true;
        }
    }

    /**
     * 手机号码验证
     *
     * @param mobiles
     * @return
     */
    public static boolean isMobileNO(String mobiles) {
        if (!TextUtils.isEmpty(mobiles) && mobiles.startsWith("1") && mobiles.length() == 11) {
            return true;
        }
        return false;
    }

    /**
     * 手机号码验证
     *
     * @param mobiles
     * @return
     */
    public static boolean isMobileApplyForConvoyNO(String mobiles) {
        if (!TextUtils.isEmpty(mobiles) && (mobiles.length() >= 5 && mobiles.length() <= 13)) {
            return true;
        }
        return false;
    }

    /**
     * 校验电话号码是否是12位以内
     * @param mobiles
     * @return
     */
    public static boolean isMobileLimit12(String mobiles) {
        return mobiles != null && mobiles.length() <= 12;
    }

    /**
     * 银行卡号校验
     *
     * @param number
     * @return
     */
    public static boolean isBankCardNO(String number) {
        //if (!TextUtils.isEmpty(number) && (number.length() >= 16)) {
        if (!TextUtils.isEmpty(number)) {
            return true;
        }
        return false;
    }

    /**
     * 手机号码验证
     *
     * @param mobiles
     * @return
     */
    public static boolean isMobile2NO(String mobiles) {
        if (!TextUtils.isEmpty(mobiles) && (mobiles.length() >= 5 && mobiles.length() <= 13)) {
            return true;
        }
        return false;
    }

    /**
     * 快递单号验证
     *
     * @param num
     * @return
     */
    public static boolean isExpressNO(String num) {
        String strPattern = "[a-zA-Z0-9]{8,14}";
        Pattern p = Pattern.compile(strPattern);
        Matcher m = p.matcher(num);
        if (m.matches()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 是否为email
     *
     * @param strEmail
     * @return
     */
    public static boolean isEmail(String strEmail) {
        String strPattern = "^([a-zA-Z0-9_\\-.]+)@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$";
        Pattern p = Pattern.compile(strPattern);
        Matcher m = p.matcher(strEmail);
        return m.matches();
    }

    //    *
//    * 显示toast，自己定义显示长短。
//    * param1:activity  传入context
//    * param2:word   我们需要显示的toast的内容
//    * param3:time length  long类型，我们传入的时间长度（如500）
    public static void showToast(final Activity activity, final String word, final long time) {
        activity.runOnUiThread(new Runnable() {
            public void run() {
                final Toast toast = Toast.makeText(activity, word, Toast.LENGTH_LONG);
                toast.show();
                Handler handler = new Handler();
                handler.postDelayed(new Runnable() {
                    public void run() {
                        toast.cancel();
                    }
                }, time);
            }
        });
    }

    public static String getProductImageUrl(String img) {
        if (TextUtils.isEmpty(img)) {
            return img;
        }
        if (img.startsWith("http") || img.startsWith("Http")) {
            return img;
        } else {
            return AppNetConfig.LORD_IMAGE + img;
        }
    }

    public static String getImageUrl(String img) {
        if (TextUtils.isEmpty(img)) {
            return img;
        }
        if (img.startsWith("http") || img.startsWith("Http")) {
            return img;
        } else {
            if (TextUtils.isDigitsOnly(img.substring(0, img.lastIndexOf(".")).trim())) {//产品
                return AppNetConfig.LORD_IMAGE + img;
            }
            return AppNetConfig.getCDNHost() + img;
        }
    }


    public static Handler getHandler() {
        return YBMAppLike.handler;
    }

    /**
     * 扩大View的触摸和点击响应范围,最大不超过其父View范围
     *
     * @param view
     * @param top
     * @param bottom
     * @param left
     * @param right
     */
    public static void expandViewTouchDelegate(final View view, final int top,
                                               final int bottom, final int left, final int right) {

        ((View) view.getParent()).post(new Runnable() {
            @Override
            public void run() {
                Rect bounds = new Rect();
                view.setEnabled(true);
                view.getHitRect(bounds);

                bounds.top -= top;
                bounds.bottom += bottom;
                bounds.left -= left;
                bounds.right += right;

                TouchDelegate touchDelegate = new TouchDelegate(bounds, view);

                if (View.class.isInstance(view.getParent())) {
                    ((View) view.getParent()).setTouchDelegate(touchDelegate);
                }
            }
        });
    }

    /**
     * 还原View的触摸和点击响应范围,最小不小于View自身范围
     *
     * @param view
     */
    public static void restoreViewTouchDelegate(final View view) {

        ((View) view.getParent()).post(new Runnable() {
            @Override
            public void run() {
                Rect bounds = new Rect();
                bounds.setEmpty();
                TouchDelegate touchDelegate = new TouchDelegate(bounds, view);

                if (View.class.isInstance(view.getParent())) {
                    ((View) view.getParent()).setTouchDelegate(touchDelegate);
                }
            }
        });
    }

    /**
     * 给出url，获取视频的第一帧
     *
     * @param url
     * @return
     */
    public static Bitmap getVideoThumbnail(String url) {
        Bitmap bitmap = null;
        //MediaMetadataRetriever 是android中定义好的一个类，提供了统一
        //的接口，用于从输入的媒体文件中取得帧和元数据；
        MediaMetadataRetriever retriever = new MediaMetadataRetriever();
        try {
            //根据文件路径获取缩略图
            retriever.setDataSource(url, new HashMap<String, String>());
            //获得第一帧图片
            bitmap = retriever.getFrameAtTime(0, MediaMetadataRetriever.OPTION_NEXT_SYNC);
        } catch (Exception e) {
            e.printStackTrace();
            BugUtil.sendBug(e);
        } finally {
            try {
                retriever.release();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return bitmap;
    }


    public static int sp2px(int sp) {
        final float fontScale = getContext().getResources().getDisplayMetrics().scaledDensity;
        return (int) (sp * fontScale + 0.5f);
    }

    /**
     * 判断字符串是否全部为中文字符组成
     *
     * @param str 检测的文字
     * @return true：为中文字符串，false:含有非中文字符
     */
    public static boolean isChineseStr(String str) {
        Pattern pattern = Pattern.compile("[\u4e00-\u9fa5]");
        char c[] = str.toCharArray();
        for (int i = 0; i < c.length; i++) {
            Matcher matcher = pattern.matcher(String.valueOf(c[i]));
            if (!matcher.matches()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断字符串是否全部为中文和字母字符组成  加了（） _还有数字
     *
     * @param str 检测的文字
     * @return true：为中文和字母字符串，false:含有非中文字符
     */
    public static boolean isEnOrCNStrOrNumber(String str) {
        Pattern pattern = Pattern.compile("^[0-9a-zA-Z\u4E00-\u9FA5_（）()]+$");
        char c[] = str.toCharArray();
        for (int i = 0; i < c.length; i++) {
            Matcher matcher = pattern.matcher(String.valueOf(c[i]));
            if (!matcher.matches()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 禁止EditText输入空格
     *
     * @param editText
     */
    public static void setEditTextInhibitInputSpace(EditText editText) {
        InputFilter filter = new InputFilter() {
            @Override
            public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                if (source.equals(" "))
                    return "";
                else
                    return null;
            }
        };
        editText.setFilters(new InputFilter[]{filter});
    }

    /**
     * RecyclerView 移动到当前位置，
     *
     * @param manager
     * @param mRecyclerView
     * @param n
     */
    public static void MoveToPosition(WrapLinearLayoutManager manager, RecyclerView mRecyclerView, int n) {


        int firstItem = manager.findFirstVisibleItemPosition();
        int lastItem = manager.findLastVisibleItemPosition();
        if (n <= firstItem) {
            mRecyclerView.scrollToPosition(n);
        } else if (n <= lastItem) {
            int top = mRecyclerView.getChildAt(n - firstItem).getTop();
            mRecyclerView.scrollBy(0, top);
        } else {
            mRecyclerView.scrollToPosition(n);
        }

    }

    /**
     * RecyclerView 移动到当前位置，
     *
     * @param manager
     * @param n
     */
    public static void MoveToPosition(WrapLinearLayoutManager manager, int n) {
        manager.scrollToPositionWithOffset(n, 0);
//        manager.setStackFromEnd(true);
    }

    public static void MoveToPosition(GridLayoutManager manager, int n) {
        manager.scrollToPositionWithOffset(n, 0);
//        manager.setStackFromEnd(true);
//        manager.setReverseLayout();
    }

    public static void MoveToPosition(RecyclerView mRecyclerView) {
        mRecyclerView.smoothScrollToPosition(0);
    }

    /*
     * recyclerView监听滚动距离
     * */
    public int getScollYDistance(WrapLinearLayoutManager manager) {
        int position = manager.findFirstVisibleItemPosition();
        View firstVisiableChildView = manager.findViewByPosition(position);
        int firstVisiableChildViewTop = firstVisiableChildView.getTop();
        int itemHeight = firstVisiableChildView.getHeight();
        //可见的item的index*item高度-最顶端位置
        return (position) * itemHeight - firstVisiableChildViewTop;
    }

    /**
     * 区间价格判断控制
     */
    public static String showProductPrice(RowsBean rowsBean) {
        String productPrice = "¥" + String.valueOf(transform(rowsBean.getFob()));

        if (rowsBean.getSkuPriceRangeList() != null && rowsBean.getSkuPriceRangeList().size() > 0) {

            double priceOne = rowsBean.getSkuPriceRangeList().get(0).price;
            double priceTwo = rowsBean.getSkuPriceRangeList().get(rowsBean.getSkuPriceRangeList().size() - 1).price;
            try {
                double num = 0;
                if (priceOne > priceTwo) {
                    num = priceOne;
                    priceOne = priceTwo;
                    priceTwo = num;
                }
            } catch (Exception e) {
                priceOne = 0;
                priceTwo = 0;
            }
            productPrice = "¥\t" + transform(priceOne) + "-" + transform(priceTwo);
        }
        return productPrice;
    }

    /**
     * 给tabLayout的导航条设置宽度
     *
     * @param tabLayout 当前tabLayout
     * @param margin    设置tab左右间距
     */
    public static void reflex(final TabLayout tabLayout, final int margin) {
        if (tabLayout == null) {
            return;
        }
        //了解源码得知 线的宽度是根据 tabView的宽度来设置的
        tabLayout.post(new Runnable() {
            @Override
            public void run() {
                try {
                    //拿到tabLayout的mTabStrip属性
                    LinearLayout mTabStrip = (LinearLayout) tabLayout.getChildAt(0);

                    for (int i = 0; i < mTabStrip.getChildCount(); i++) {
                        View tabView = mTabStrip.getChildAt(i);

                        //拿到tabView的mTextView属性  tab的字数不固定一定用反射取mTextView
                        Field mTextViewField = tabView.getClass().getDeclaredField("mTextView");
                        mTextViewField.setAccessible(true);

                        TextView mTextView = (TextView) mTextViewField.get(tabView);

                        tabView.setPadding(0, 0, 0, 0);

                        //因为我想要的效果是   字多宽线就多宽，所以测量mTextView的宽度
                        int width = 0;
                        width = mTextView.getWidth();
                        if (width == 0) {
                            mTextView.measure(0, 0);
                            width = mTextView.getMeasuredWidth();
                        }

                        //设置tab左右间距为Xdp  注意这里不能使用Padding 因为源码中线的宽度是根据 tabView的宽度来设置的
                        LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) tabView.getLayoutParams();
                        params.width = width;
                        params.leftMargin = margin;
                        params.rightMargin = margin;
                        tabView.setLayoutParams(params);

                        tabView.invalidate();
                    }

                } catch (NoSuchFieldException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    /**
     * 判断手机是否ROOT
     */
    public static boolean isRoot() {

        boolean root = false;

        try {
            if ((!new File("/system/bin/su").exists())
                    && (!new File("/system/xbin/su").exists())) {
                root = false;
            } else {
                root = true;
            }

        } catch (Exception e) {
        }

        return root;
    }

    public static Drawable getDrawable(Context context, int res) {

        Drawable drawable = null;
        try {
            drawable = context.getResources().getDrawable(res);
        } catch (Resources.NotFoundException e) {
            e.printStackTrace();
        }
        return drawable;
    }

    //获取虚拟按键的高度
    public static int getNavigationBarHeight(Context context) {
        int result = 0;
        if (hasNavBar(context)) {
            Resources res = context.getResources();
            int resourceId = res.getIdentifier("navigation_bar_height", "dimen", "android");
            if (resourceId > 0) {
                result = res.getDimensionPixelSize(resourceId);
            }
        }
        return result;
    }

    /**
     * 检查是否存在虚拟按键栏
     *
     * @param context
     * @return
     */
    @TargetApi(Build.VERSION_CODES.ICE_CREAM_SANDWICH)
    public static boolean hasNavBar(Context context) {
        Resources res = context.getResources();
        int resourceId = res.getIdentifier("config_showNavigationBar", "bool", "android");
        if (resourceId != 0) {
            boolean hasNav = res.getBoolean(resourceId);
            // check override flag
            String sNavBarOverride = getNavBarOverride();
            if ("1".equals(sNavBarOverride)) {
                hasNav = false;
            } else if ("0".equals(sNavBarOverride)) {
                hasNav = true;
            }
            return hasNav;
        } else { // fallback
            return !ViewConfiguration.get(context).hasPermanentMenuKey();
        }
    }

    /**
     * 判断虚拟按键栏是否重写
     *
     * @return
     */
    private static String getNavBarOverride() {
        String sNavBarOverride = null;
        try {
            Class c = Class.forName("android.os.SystemProperties");
            Method m = c.getDeclaredMethod("get", String.class);
            m.setAccessible(true);
            sNavBarOverride = (String) m.invoke(null, "qemu.hw.mainkeys");
        } catch (Throwable e) {
        }
        return sNavBarOverride;
    }


    private static final int PORTRAIT = 0;
    private static final int LANDSCAPE = 1;
    @NonNull
    private volatile static Point[] mRealSizes = new Point[2];

    public static int getScreenRealHeight(@Nullable Context context) {

        int orientation = context != null
                ? context.getResources().getConfiguration().orientation
                : Abase.getResources().getConfiguration().orientation;
        orientation = orientation == Configuration.ORIENTATION_PORTRAIT ? PORTRAIT : LANDSCAPE;

        if (mRealSizes[orientation] == null) {
            WindowManager windowManager = context != null
                    ? (WindowManager) context.getSystemService(Context.WINDOW_SERVICE)
                    : (WindowManager) Abase.getContext().getSystemService(Context.WINDOW_SERVICE);
            if (windowManager == null) {
                return getScreenHeight(context);
            }
            Display display = windowManager.getDefaultDisplay();
            Point point = new Point();
            display.getRealSize(point);
            mRealSizes[orientation] = point;
        }
        return mRealSizes[orientation].y;
    }

    public static int getScreenHeight(@Nullable Context context) {
        if (context != null) {
            return context.getResources().getDisplayMetrics().heightPixels;
        }
        return 0;
    }

    /**
     * 判断是否是全面屏
     */
    private volatile static boolean mHasCheckAllScreen;
    private volatile static boolean mIsAllScreenDevice;

    public static boolean isAllScreenDevice(Context context) {
        if (mHasCheckAllScreen) {
            return mIsAllScreenDevice;
        }
        mHasCheckAllScreen = true;
        mIsAllScreenDevice = false;
        // 低于 API 21的，都不会是全面屏。。。
        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        if (windowManager != null) {
            Display display = windowManager.getDefaultDisplay();
            Point point = new Point();
            display.getRealSize(point);
            float width, height;
            if (point.x < point.y) {
                width = point.x;
                height = point.y;
            } else {
                width = point.y;
                height = point.x;
            }
            if (height / width >= 1.97f) {
                mIsAllScreenDevice = true;
            }
        }
        return mIsAllScreenDevice;
    }

    /**
     * 获取状态栏高度
     *
     * @return
     */
    public static int getStatusBarHeight(Context context) {
        int result = 0;
        int resultOfDp = context.getResources().getIdentifier("status_bar_height", "dimen", "android");
        if (resultOfDp > 0) {
            result = context.getResources().getDimensionPixelSize(resultOfDp);
        }
        return result;
    }

    /**
     * Return the application's height of screen, in pixel.
     *
     * @return the application's height of screen, in pixel
     */
    public static int getAppScreenHeight() {
        WindowManager wm = (WindowManager) BaseYBMApp.getApp().getSystemService(Context.WINDOW_SERVICE);
        if (wm == null) {
            return -1;
        }
        Point point = new Point();
        wm.getDefaultDisplay().getSize(point);
        return point.y;
    }


    public static int getColorFromAptitudeStatus(int status) {
        int color = R.color.color_text_base_color;
        switch (status) {
            case STATUS_CANCELLATION://7作废 红色
                color = R.color.color_text_status_red;
                break;
            case STATUS_STEP1://2一审中 绿色
            case STATUS_STEP2://3 二审中
            case STATUS_RECYCLED://6资质已回收
                color = R.color.color_theme_base_color;
                break;
            case STATUS_UNCOMMITTED://1
            case STATUS_REJECTED://4
            case STATUS_AWAITS_RECYCLING://5
            case STATUS_DISQUALIFICATION://8
            case STATUS_PAST_DUE://9
            case STATUS_FIRST_APTITUDE_NO_RECYCLE://10
                break;
        }
        return getColor(color);
    }

    public static int getColorFromLogStatus(String status) {
        int color = R.color.color_text_base_color;
        switch (TextUtils.isEmpty(status)?"":status) {
            case STATUS_REJECT://0
                color = R.color.color_text_status_red;
                break;
            case STATUS_PASS://1绿色
                color = R.color.color_theme_base_color;
                break;
        }
        return getColor(color);
    }

    /**
     * 获取带格式是的价格
     *
     * @param src
     * @return
     */
    public static SpannableStringBuilder getPriceWithFormat(String src, int littleSize) {
        SpannableStringBuilder builder = new SpannableStringBuilder(src);
        if (src == null) return builder;
        if (!src.contains(".")) builder.append(".00");
        int index = builder.toString().indexOf(".");
        AbsoluteSizeSpan symbolSpan = new AbsoluteSizeSpan(ConvertUtils.dp2px(littleSize));
        AbsoluteSizeSpan littleSpan = new AbsoluteSizeSpan(ConvertUtils.dp2px(littleSize));
        builder.setSpan(symbolSpan, 0, 1, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
        builder.setSpan(littleSpan, index, builder.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
        return builder;
    }

    public static SpannableStringBuilder getPriceWithFormat(String src, int littleSize, boolean isFormat) {
        if (isFormat) return getPriceWithFormat(src, littleSize);
        else return new SpannableStringBuilder(src);
    }

    public static SpannableStringBuilder getPriceWithFormat(String src) {
        return getPriceWithFormat(src, 10);
    }

    /**
     * 千位分隔符,并且小数点后保留2位
     *
     * @param num
     * @return String
     */
    public static String getMicrometerLevelFomat(double num) {
        try {
            DecimalFormat df = new DecimalFormat("#,##0.00");
            return df.format(num);
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return "0.00";
    }
    /**
     * 将每三个数字加上逗号处理（通常使用金额方面的编辑）
     *
     * @param str 需要处理的字符串
     * @return 处理完之后的字符串
     */
    public static String addComma(String str) {
        DecimalFormat decimalFormat = new DecimalFormat(",###");
        return decimalFormat.format(Double.parseDouble(str));
    }

    /**
     * 判断Activity是否存在
     * @param activity
     * @return
     */
    public static boolean isLiving(Activity activity) {
        if (activity == null) {
            return false;
        }
        if (activity.isFinishing()) {
            return false;
        }
        return true;
    }
}
