package com.ybmmarket20.utils;

import com.apkfuns.logutils.LogUtils;

import java.io.IOException;
import java.net.Proxy;
import java.net.ProxySelector;
import java.net.SocketAddress;
import java.net.URI;
import java.util.Collections;
import java.util.List;

/**
 * 代理选择器
 */

public class YBMProxySelector extends ProxySelector {
    private Proxy proxy;
    private static YBMProxySelector selector;
    private YBMProxySelector() {
        proxy = Proxy.NO_PROXY;
    }

    @Override
    public List<Proxy> select(URI uri) {
        return Collections.singletonList(proxy);
    }

    @Override
    public void connectFailed(URI uri, SocketAddress sa, IOException ioe) {
        LogUtils.d(sa);
    }

    public static YBMProxySelector getDefault(){
        if(selector == null){
            synchronized (YBMProxySelector.class){
                if(selector == null){
                    selector = new YBMProxySelector();
                }
            }
        }
        return selector;
    }
}
