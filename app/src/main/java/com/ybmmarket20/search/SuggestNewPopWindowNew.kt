package com.ybmmarket20.search

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.text.TextUtils
import android.view.*
import android.view.View.OnTouchListener
import android.widget.*
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.RecyclerView
import com.apkfuns.logutils.LogUtils
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SuggestListBean
import com.ybmmarket20.bean.SuggestShopBean
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig.ADD_HISTORY_SUGGEST
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.*
import com.ybmmarket20.view.ShopNameWithTagView
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarketkotlin.adapter.GoodListAdapterNew
import java.net.URLEncoder

/**
 * 搜索的推荐 新的样式增加单条商品信息
 */
class SuggestNewPopWindowNew(context: Context, private val url: String, private val params: RequestParams?, private val token: View) {
    var dismissShopAndProduct: Boolean = false
    private var popwindow: PopupWindow? = null
    private var rowsBean: RowsBean? = null

    private var suggestProductList: MutableList<RowsBean> = arrayListOf();
    private var suggestShopList: MutableList<SuggestShopBean> = arrayListOf();
    private var suggestTextList: MutableList<String> = arrayListOf();

    lateinit var contentView: LinearLayout
    lateinit var listView: RecyclerView

    lateinit var suggestProductAdapter: GoodListAdapterNew
    lateinit var suggestTextAdapter: SuggestTextAdapter
    lateinit var suggestShopAdapter: SuggestShopAdapter
    lateinit var concatAdapter: ConcatAdapter

    var prePageSource: String? = null
    var pageUrl: String? = null

    var merchantid: String? = null
    var listener: ItemClickListener? = null
    var onTouchListener: OnTouchListener? = null
    private var cancelHandler = false

    private var lastTime: Long = 0
    private val diffTime: Long = 800
    private val MAXTIME: Long = 800
    val allItemList = mutableListOf<Any>()

    var suggestBean: SuggestListBean? = null

    init {
        init(context)
    }

    private fun initPop(token: View) {
        popwindow = PopupWindow(contentView,
                token.width, WindowManager.LayoutParams.MATCH_PARENT, false)
        popwindow?.setBackgroundDrawable(ColorDrawable(Color.parseColor("#ee222222")))
        popwindow?.isOutsideTouchable = true
        popwindow?.animationStyle = R.style.PopupWindowAnimation
        popwindow?.inputMethodMode = PopupWindow.INPUT_METHOD_NEEDED
        popwindow?.softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING
        popwindow?.setTouchInterceptor(onTouchListener)
    }

    private fun init(context: Context) {

        val inflater = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        contentView = inflater.inflate(R.layout.pop_list_suggest, null, false) as LinearLayout
        listView = contentView.findViewById(R.id.listView)
        suggestProductAdapter = GoodListAdapterNew(R.layout.item_goods_new, suggestProductList)
        suggestShopAdapter = SuggestShopAdapter(R.layout.item_suggest_shopname, suggestShopList)
        suggestTextAdapter = SuggestTextAdapter(R.layout.list_item_suggest, suggestTextList)

        concatAdapter = ConcatAdapter(listOf(suggestProductAdapter, suggestShopAdapter, suggestTextAdapter))
        listView.adapter = concatAdapter

        listView.layoutManager = WrapLinearLayoutManager(context)
    }

    private val sugestHandler: BaseResponse<*> = object : BaseResponse<SuggestListBean>() {
        override fun onSuccess(content: String, obj: BaseBean<SuggestListBean>?, suggestBean: SuggestListBean?) {
            if (obj != null && obj.isSuccess && !cancelHandler) {
                suggestBean?.let { show(token, suggestBean) }
            }
        }
    }

    fun suggest(keyWord: String) {
        if (System.currentTimeMillis() - lastTime >= diffTime) {
            getSuggestList(keyWord, sugestHandler)
        }
        lastTime = System.currentTimeMillis()
    }

    private fun getSuggestList(keyword: String, handler: BaseResponse<*>) {
        if (!TextUtils.isEmpty(keyword) && !cancelHandler) {
            if (params != null) {
                merchantid = SpUtil.getMerchantid()
                params.put("merchantId", merchantid)
                params.put("skuName", keyword)
                params.put("keyword", keyword)
                if (prePageSource != null) {
                    params.put("pageSource", "${prePageSource}_conebox_e${URLEncoder.encode(URLEncoder.encode(keyword, "UTF-8"), "UTF-8")}")
                    params.put("nsid", "")
                    params.put("listoffset", "")
                    params.put("listdata", "")
                    params.put("pageurl", pageUrl)
                }
            }
            HttpManager.getInstance().post(url, params, handler)
        } else {
            if (isShow) {
                suggestBean?.let { updateData(it) }
            }
        }
    }

    private fun show(token: View, suggestBean: SuggestListBean) {
        if (popwindow == null) {
            initPop(token)
            this.suggestBean = suggestBean
        }
        try {
            if (isShow) {
                updateData(suggestBean)
                return
            }
        } catch (e: Exception) {
            return
        }
        try {
            if (updateData(suggestBean)) {
                if (Build.VERSION.SDK_INT >= 24) {
                    val location = IntArray(2)
                    token.getLocationOnScreen(location)
                    // 7.1 版本处理
                    if (Build.VERSION.SDK_INT == 25) {
                        //【note!】Gets the screen height without the virtual key
                        val wm = popwindow?.contentView?.context?.getSystemService(Context.WINDOW_SERVICE) as WindowManager
                        val screenHeight = wm.defaultDisplay.height
                        popwindow?.height = screenHeight - location[1] - token.height
                    }
                    popwindow?.showAtLocation(token, Gravity.NO_GRAVITY, 0, location[1] + token.height)
                } else {
                    popwindow?.showAsDropDown(token, 0, 0)
                }
                popwindow?.showAsDropDown(token, 0, 0)
            }
        } catch (e: Exception) {
            return
        }
        popwindow?.update()
    }

    private fun updateData(suggestBean: SuggestListBean): Boolean {
        allItemList.clear()
        if (dismissShopAndProduct==false){
            suggestProductList?.clear()
            suggestBean?.csuInfo?.let { suggestProductList.add(it) }
            allItemList.addAll(suggestProductList)
        }

        if (dismissShopAndProduct == false){
            suggestShopList?.clear()
            suggestBean?.suggestShopList?.let { suggestShopList.addAll(it) }
            allItemList.addAll(suggestShopList)
        }

        suggestTextList?.clear()
        suggestBean?.suggestList?.let {
            suggestTextList.addAll(it)
            allItemList.addAll(suggestTextList)
        }

        suggestProductAdapter.notifyDataSetChanged()
        suggestShopAdapter.notifyDataSetChanged()
        suggestTextAdapter.notifyDataSetChanged()

        if (suggestTextList.isNullOrEmpty() && suggestShopList.isNullOrEmpty() && suggestProductList.isNullOrEmpty()) {
            dismiss()
            return false
        }

        if (concatAdapter.itemCount <= 9) {
            popwindow?.height = WindowManager.LayoutParams.WRAP_CONTENT
        } else {
            popwindow?.height = UiUtils.getScreenHeight() * 3 / 4
        }
        return true
    }

    fun dismiss() {
        popwindow?.dismiss()
    }

    val isShow: Boolean = popwindow?.isShowing ?: false

    fun cancelHandler(cancel: Boolean) {
        cancelHandler = cancel
    }

    fun setItemClickListener(listener: ItemClickListener?) {
        this.listener = listener
    }


    inner class SuggestTextAdapter(layoutResId: Int, data: MutableList<String>?) : YBMBaseAdapter<String>(layoutResId, data) {
        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: String?) {
            baseViewHolder?.getView<TextView>(R.id.tv)?.text = t ?: ""
            baseViewHolder?.setOnClickListener(R.id.tv) {
                LogUtils.e("suggest pop potion = " + baseViewHolder.position)

                listener?.onItemClick(
                        if (rowsBean == null) t else "",
                        rowsBean?.id ?: -1,
                        baseViewHolder.position
                )
            }
        }

    }

    inner class SuggestShopAdapter(layoutResId: Int, data: MutableList<SuggestShopBean>?) : YBMBaseAdapter<SuggestShopBean>(layoutResId, data) {
        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: SuggestShopBean?) {
            whenAllNotNull(baseViewHolder, t) { holder, bean ->
                holder.itemView.setOnClickListener {
                    bean.skipUrl?.let {
                        val params = RequestParams().apply {
                            put("keyword", bean.shopName)
                            put("shopCode", bean.shopCode)
                            put("skipUrl", bean.skipUrl)
                        }
                        HttpManager.getInstance().post(ADD_HISTORY_SUGGEST, params, object : BaseResponse<Any>() {})
                        RoutersUtils.open(it)
                    }
                }
                holder.getView<TextView>(R.id.tv_shop_name)?.text = t?.shopName ?: ""
                //sug店铺标签
                val tagView = holder.getView<ShopNameWithTagView>(R.id.snwtv_sug_shop_tag)
                if (!bean.sugShopTags.isNullOrEmpty()) {
                    tagView.bindData(bean.sugShopTags, null, Int.MAX_VALUE)
                    tagView.visibility = View.VISIBLE
                } else tagView.visibility = View.GONE
                //sug店铺描述
                val tvSugShopDes = holder.getView<TextView>(R.id.tv_sug_shop_des)
                if (!bean.getSugShopDes().isNullOrEmpty()) {
                    tvSugShopDes.text = bean.getSugShopDes()
                    tvSugShopDes.visibility = View.VISIBLE
                } else tvSugShopDes.visibility = View.GONE
            }
        }
    }

    interface ItemClickListener {
        fun onItemClick(str: String?, id: Long, position: Int)
    }

}