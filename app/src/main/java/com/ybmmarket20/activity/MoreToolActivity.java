package com.ybmmarket20.activity;

import android.view.View;

import androidx.lifecycle.ViewModelProvider;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybmmarket20.R;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.viewmodel.ElsePageViewModel;

import butterknife.OnClick;

/**
 * 我的-更多工具
 */
@Router("moretool")
public class MoreToolActivity extends BaseActivity {
    private ElsePageViewModel mViewModel;
    @Override
    protected void initData() {
        mViewModel = new ViewModelProvider(this).get(ElsePageViewModel.class);
        setTitle("更多工具");
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_more_tool;
    }

    @OnClick({R.id.ll_clause, R.id.ll_sale_rule})
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.ll_clause:  //服务条款
                mViewModel.getLoginAgreement(2);
                break;
            case R.id.ll_sale_rule:   //售后规则
                RoutersUtils.open("ybmpage://commonh5activity?url=" + AppNetConfig.RULE);
                break;
        }
    }
}
