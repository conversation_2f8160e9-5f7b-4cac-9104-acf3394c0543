package com.ybmmarket20.activity

import android.os.Bundle
import com.ybmmarket20.R
import com.ybmmarket20.adapter.AgentOrderPageAdapter
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.LazyFragment
import com.ybmmarket20.fragments.AuthorizationAreaFragment
import kotlinx.android.synthetic.main.activity_authorization_area.*

/**
 * 授权专区
 * <AUTHOR>
 * 2019-10-14
 */

const val AUTHORIZATION_AREA_ALL = -1
const val AUTHORIZATION_AREA_AUTHORIZATIONED = 1
const val AUTHORIZATION_AREA_UNAUTHORIZATION = 0

class AuthorizationAreaActivity: BaseActivity() {

    override fun getContentViewId(): Int = R.layout.activity_authorization_area

    override fun initData() {
        val fragments = ArrayList<LazyFragment>()
        fragments.add(createFragments(AUTHORIZATION_AREA_ALL))
        fragments.add(createFragments(AUTHORIZATION_AREA_AUTHORIZATIONED))
        fragments.add(createFragments(AUTHORIZATION_AREA_UNAUTHORIZATION))
        val pageAdapter = AgentOrderPageAdapter(supportFragmentManager, fragments)
        nvp.offscreenPageLimit = 3
        nvp.adapter = pageAdapter
        nvp.setScroll(false)
        stl.setViewPager(nvp)
        stl.setIndicatorWidthEqualTitleHalf(true)
        tv_order_area.setOnClickListener {
            gotoAtivity(AgentOrderActivity::class.java)
            finish()
        }

    }

    /**
     * 创建Fragment集合
     */
    private fun createFragments(authorizationCategory: Int): LazyFragment {
        return AuthorizationAreaFragment().apply {
            val bundle = Bundle()
            bundle.putInt("authorizationCategory", authorizationCategory)
            arguments = bundle
        }
    }
}