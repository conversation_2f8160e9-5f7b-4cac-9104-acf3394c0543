package com.ybmmarket20.activity

import android.view.View
import androidx.activity.viewModels
import androidx.lifecycle.Observer
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.viewmodel.AssociateShopsViewModel
import com.ybmmarket20.viewmodel.ClerkAptitudeAuthenticationViewModel
import kotlinx.android.synthetic.main.activity_add_aptitude.*
import kotlinx.android.synthetic.main.common_header_items.*

/**
 * 店员资质认证
 */
@Router("clerkaptitudeauthenticationactivity")
class ClerkAptitudeAuthenticationActivity: AddAptitudeActivity(), View.OnClickListener {

    val mViewModel: ClerkAptitudeAuthenticationViewModel by viewModels()
    val associatedShopViewModel: AssociateShopsViewModel by viewModels()

    override fun initData() {
        super.initData()
        initObserver()
        val status = intent.getStringExtra("status")?.toInt()
        showProgress()
        mViewModel.getLayoutData(status != 0, mMerchantId)
        apv.visibility = View.GONE
        tvTopTips.text = "*请上传资质进行关联审核，审核通过后可登入药帮忙APP"
        iv_top_tips_delete.visibility = View.GONE
        llTopTips.visibility = View.VISIBLE
        edit.visibility = View.GONE
        tvConfirm.visibility = View.VISIBLE
        tvConfirm.setOnClickListener(this)
        iv_back.setOnClickListener {
            finish()
        }
    }

    override fun onBackPressed() {
        finish()
    }

    fun initObserver() {
        mViewModel.layoutLiveData.observe(this, Observer {
            dismissProgress()
            mNecessaryList = it
            renderUi()
            setTitle("资质认证")
        })

        mViewModel.confirmLiveData.observe(this, Observer {
            dismissProgress()
            if (it.isSuccess) {
                associatedShopViewModel.queryAssociatedShops()
            }
        })
        
        associatedShopViewModel.queryAssociateShopsLiveData.observe(this) {
            if (it.isSuccess) {
                if ((it.data.list?.size ?: 1) > 1) {
                    RoutersUtils.open("ybmpage://associatedshopauthenticationprocessing?status=2&merchantId=$mMerchantId")
                } else {
                    RoutersUtils.open("ybmpage://associatedshopauthenticationprocessing?status=1&merchantId=$mMerchantId")
                }
            }
        }
    }

    override fun getExamplePic() {}

    override fun addRemark() {}

    override fun onClick(v: View?) {
        when(v?.id) {
            R.id.tvConfirm -> {
                showProgress()
                mViewModel.confirmClerkAptitudeAuthentication(mNecessaryList, mMerchantId)
            }
        }
    }

}