package com.ybmmarket20.activity

import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.xyyreport.page.orderDetail.OrderDetailReport

abstract class OrderDetailAnalysisActivity: BaseActivity() {

    private var isPv = false

    fun pvTrack(orderNo: String?) {
        if (isPv) return
        isPv = true
        OrderDetailReport.pvTrack(this, orderNo)
    }

    fun trackBuyAgainClick(){}

}