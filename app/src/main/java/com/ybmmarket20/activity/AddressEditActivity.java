package com.ybmmarket20.activity;

import android.app.Activity;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.View;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.AddressListBean;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.Province;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.MyImageSpan;
import com.ybmmarket20.view.jdaddressselector.AddressProvider;
import com.ybmmarket20.view.jdaddressselector.BottomDialog;
import com.ybmmarket20.view.jdaddressselector.OnAddressSelectedListener;

import java.util.List;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 添加地址
 */
@Router({"addressedit", "addressedit/:update", "addressnew"})
public class AddressEditActivity extends BaseActivity implements OnAddressSelectedListener {


    @Bind(R.id.et_name)
    EditText etName;
    @Bind(R.id.et_mobile)
    EditText etMobile;
    @Bind(R.id.et_address)
    TextView etAddress;
    @Bind(R.id.et_remark)
    TextView etRemark;
    @Bind(R.id.ll_remark)
    LinearLayout llRemark;
    @Bind(R.id.et_remark_input)
    EditText etRemarkInput;
    @Bind(R.id.ll_remark_input)
    LinearLayout llRemarkInput;
    @Bind(R.id.btn_ok)
    TextView btnOk;
    @Bind(R.id.tv_tips)
    TextView tvTips;
    @Bind(R.id.cb_setting)
    CheckBox cbSetting;
    @Bind(R.id.ll_set_check)
    RelativeLayout llSetCheck;
    @Bind(R.id.tv_show_default)
    TextView tvShowDefault;


    private boolean getSucc = false;
    private boolean canUpdate = false;
    private boolean update = false;
    private AddressListBean bean;
    private String updateStr;
    private boolean isEdit = false;
    String mCurrentProviceName = "";
    String mCurrentCityName = "";
    String mCurrentDistrictName = "";
    String mCurrentProvinceId = "";
    String mCurrentCityId = "";
    String mCurrentDistrictId = "";

    private String addressType;

    //如果是编辑地址
    @Override
    protected void initData() {
        setTitle("编辑地址");
        //addressIndex
        updateStr = getIntent().getStringExtra("update");
        bean = (AddressListBean) getIntent().getSerializableExtra("data");
        addressType = getIntent().getStringExtra("addressType");
        if (TextUtils.isEmpty(updateStr)) {//查看地址
            btnOk.setVisibility(View.INVISIBLE);
            etName.setEnabled(false);
            etMobile.setEnabled(false);
            etRemarkInput.setEnabled(false);
            isEdit = true;
            setTitle("地址管理");
            setRigthText(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (isEdit) {
                        getRigthText().setText("取消");
                        etName.setEnabled(true);
                        etMobile.setEnabled(true);
                        etRemarkInput.setEnabled(true);
                        setTitle("编辑地址");
                        btnOk.setVisibility(View.VISIBLE);
                        if (!TextUtils.isEmpty(etName.getText())) {
                            etName.setSelection(etName.getText().length());
                        }
                    } else {
                        getRigthText().setText("编辑");
                        etName.setEnabled(false);
                        etMobile.setEnabled(false);
                        etRemarkInput.setEnabled(false);
                        btnOk.setVisibility(View.INVISIBLE);
                        setTitle("地址管理");
                    }
                    isEdit = !isEdit;
                }
            }, "编辑");
        }
        btnOk.setEnabled(false);
        if (bean != null && bean.isdefault) {
            tvShowDefault.setVisibility(View.VISIBLE);
            llSetCheck.setVisibility(View.GONE);
            btnOk.setText("保存并使用");
        } else {
            tvShowDefault.setVisibility(View.GONE);
            llSetCheck.setVisibility(View.VISIBLE);
        }
        bindImageData(getResources().getString(R.string.address_tips2));
        getAddress();
    }

    private void setResult() {
        Intent intent = new Intent();
        Bundle bundle = new Bundle();
        bundle.putSerializable("addressIndex", bean);
        intent.putExtras(bundle);
        setResult(Activity.RESULT_OK, intent);
    }

    /**
     * 设置UI布局
     **/
    @Override
    public int getContentViewId() {
        return R.layout.activity_address_edit;
    }

    private BottomDialog dialog;

    /****
     * 事件，选择地址事件暂时不加，地址需审核才可以修改
     * **/
    @OnClick({R.id.btn_ok, R.id.ll_set_check})
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.et_address:
                //关闭软键盘
                hideSoftInput();
                dialog = new BottomDialog(AddressEditActivity.this);
                dialog.show();
                dialog.setOnAddressSelectedListener(AddressEditActivity.this);
                dialog.setAddressProvider(addressListener);
                break;
            case R.id.btn_ok:
                //关闭软键盘
                hideSoftInput();
                editAddress();
                break;
            case R.id.ll_set_check:
                if (cbSetting == null) {
                    return;
                }
                if (cbSetting.isChecked()) {
                    close();
                } else {
                    open();
                }
                break;
        }
    }

    private void close() {
        if (cbSetting == null) {
            return;
        }
        cbSetting.setChecked(false);
    }

    private void open() {
        if (cbSetting == null) {
            return;
        }
        cbSetting.setChecked(true);
    }

    //编辑地址
    private void editAddress() {
        String userName = etName.getText().toString().trim();
        String phone = etMobile.getText().toString().trim();
        if (TextUtils.isEmpty(userName)) {
            ToastUtils.showShort("请输入用户名");
            return;
        }
        if (userName.length() < 2) {
            ToastUtils.showShort("姓名不能少于2个字哦");
            return;
        } else if (userName.length() > 8) {
            ToastUtils.showShort("姓名不能大于8个字哦");
            return;
        }
        if (!UiUtils.isMobileLimit12(phone)) {
            ToastUtils.showShort("请输入正确的手机号码");

            return;
        }
        boolean editRemark = false;
        if (getSucc && canUpdate) {
            String remark = etRemarkInput.getText().toString().trim();
            if (TextUtils.isEmpty(remark)) {
                editRemark = false;
            } else {
                editRemark = true;
            }
        }
//        if (!editRemark) {
//            if (userName.equals(bean.getContactor()) && phone.equals(bean.mobile)) {//没有修改过东西
//                ToastUtils.showShort("请修改后在提交");
//                return;
//            }
//        }
        btnOk.setEnabled(false);
        showProgress();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);//商户ID
        params.put("contactor", userName);//收货人
        params.put("mobile", phone);//手机号
        params.put("id", bean.id + "");//收货地址编号
        if (!bean.isdefault) {
            params.put("isdefault", cbSetting.isChecked() ? "1" : "0");//是否默认地址
        }
        if (editRemark) {
            params.put("remark", etRemarkInput.getText().toString().trim());//备注地址
        }
        params.put("addressType", addressType);
        final boolean finalEditRemark = editRemark;
        HttpManager.getInstance().post(AppNetConfig.EDIT_ADDRESS, params, new BaseResponse<EmptyBean>() {

            @Override
            public void onFailure(NetError error) {
                btnOk.setEnabled(true);
                dismissProgress();
            }

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean data) {
                btnOk.setEnabled(true);
                dismissProgress();
                if (obj != null) {
                    if (obj.isSuccess()) {
                        if (!TextUtils.isEmpty(updateStr)) {//修改成功了
                            update = true;
                        }
                        bean.setMobile(etMobile.getText().toString().trim());
                        bean.setContactor(etName.getText().toString().trim());
                        if (!bean.isdefault) {
                            bean.isdefault = cbSetting.isChecked();
                        }

                        if (finalEditRemark) {//不可以修改了
                            //V9.3地址备注不显示
//                            llRemarkInput.setVisibility(View.GONE);
//                            llRemark.setVisibility(View.VISIBLE);
//                            findViewById(R.id.view_line).setVisibility(View.VISIBLE);
                            etRemark.setText(etRemarkInput.getText().toString().trim());
                            bean.setRemark(etRemarkInput.getText().toString().trim());
                        }
                        if (update) {//修改编辑了
                            setResult();
                        } else {
                            ToastUtils.showShort("修改成功");
                        }
                        finish();
                    }
                }
            }
        });
    }


    //获取地址
    private void getAddress() {
        if (bean != null) {
            getSucc = true;
            btnOk.setEnabled(true);
            canUpdate = bean.isUpdateAddress();
            if (!bean.isUpdateAddress()) {
                //V9.3地址备注不显示
//                llRemarkInput.setVisibility(View.GONE);
//                llRemark.setVisibility(View.VISIBLE);
//                findViewById(R.id.view_line).setVisibility(View.VISIBLE);
//                etRemark.setText(bean.getRemark());
//                if (TextUtils.isEmpty(bean.getRemark())) {
//                    etRemark.setHint("");
//                }
            } else {//可以输入
                //  7.28不可编辑地址备注
                //  llRemarkInput.setVisibility(View.VISIBLE);
                //  llRemark.setVisibility(View.GONE);
                //findViewById(R.id.view_line).setVisibility(View.VISIBLE);
            }
            etName.setText(bean.getContactor());
            etMobile.setText(bean.getMobile());
            etAddress.setText(bean.getFullAddress());
            if (!TextUtils.isEmpty(etName.getText())) {
                etName.setSelection(etName.getText().length());
            }
        }
    }

    private void bindImageData(String text) {

//        List<Integer> list = new ArrayList<>();
//        list.add(R.drawable.icon_address_message_tips);
//
//        SpannableStringBuilder shopName = getShopNameIcon(text, list);
//        if (!TextUtils.isEmpty(shopName)) tvTips.setText(shopName);
        tvTips.setText(text);
    }

    private SpannableStringBuilder getShopNameIcon(String shopName, List<Integer> icons) {
        if (icons != null && icons.size() > 0) {
            SpannableStringBuilder spannableString = new SpannableStringBuilder(shopName);
            for (int i = 0; i < icons.size(); i++) {
                Drawable drawable = getResources().getDrawable(icons.get(i));
                drawable.setBounds(0, 0, ConvertUtils.dp2px(15), ConvertUtils.dp2px(15));

                MyImageSpan imageSpan = new MyImageSpan(drawable, 2);
                //占个位置
                spannableString.insert(0, "-");
                spannableString.setSpan(imageSpan, 0, 1, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            }
            return spannableString;
        }
        return null;
    }


    AddressProvider addressListener = new AddressProvider() {
        @Override
        public void provideProvinces(final AddressReceiver<Province> addressReceiver) {
            RequestParams params = RequestParams.newBuilder().url(AppNetConfig.FIND_AREA_NEW)
                    .addParam("parentId", "0").build();
            HttpManager.getInstance().post(params, new BaseResponse<List<Province>>() {
                @Override
                public void onSuccess(String content, BaseBean<List<Province>> bean, List<Province> data) {
                    if (bean == null || !bean.isSuccess()) {
                        return;
                    }
                    List<Province> provinces = bean.data;
                    addressReceiver.send(provinces);
                }
            });
        }

        @Override
        public void provideCitiesWith(String provinceId, final AddressReceiver<Province> addressReceiver) {
            RequestParams params = RequestParams.newBuilder().url(AppNetConfig.FIND_AREA_NEW)
                    .addParam("parentId", "" + provinceId).build();
            HttpManager.getInstance().post(params, new BaseResponse<List<Province>>() {
                @Override
                public void onSuccess(String content, BaseBean<List<Province>> bean, List<Province> data) {
                    if (bean == null || !bean.isSuccess()) {
                        return;
                    }
                    List<Province> provinces = bean.data;
                    addressReceiver.send(provinces);
                }
            });
        }

        @Override
        public void provideCountiesWith(String cityId, final AddressReceiver<Province> addressReceiver) {
            RequestParams params = RequestParams.newBuilder().url(AppNetConfig.FIND_AREA_NEW)
                    .addParam("parentId", "" + cityId).build();
            HttpManager.getInstance().post(params, new BaseResponse<List<Province>>() {
                @Override
                public void onSuccess(String content, BaseBean<List<Province>> bean, List<Province> data) {
                    if (bean == null || !bean.isSuccess()) {
                        return;
                    }
                    List<Province> provinces = bean.data;
                    addressReceiver.send(provinces);
                }
            });
        }

        @Override
        public void provideStreetsWith(String countyId, AddressReceiver<Province> addressReceiver) {
            // TODO: 2019-12-26 因地址需要审核，编辑暂不可用，这块逻辑之后如需编辑要完善补充
        }
    };

    @Override
    public void onAddressSelected(Province province, Province city, Province county, Province street) {
        if (dialog != null) {
            try {
                dialog.dismiss();
            } catch (Exception e) {
            }
        }
        if (province == null || city == null) {
            return;
        }

        String address = province.areaName + city.areaName + (county == null ? "" : county.areaName);
        etAddress.setText(address);
        mCurrentProviceName = province.areaName;
        mCurrentCityName = city.areaName;
        mCurrentDistrictName = county == null ? "" : county.areaName;
        mCurrentProvinceId = province.areaCode;
        mCurrentCityId = city.areaCode;
        mCurrentDistrictId = county == null ? "" : county.areaCode;
    }
}
