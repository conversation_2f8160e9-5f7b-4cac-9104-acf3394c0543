package com.ybmmarket20.activity

import android.animation.ObjectAnimator
import android.content.Context
import android.graphics.Rect
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Group
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.github.mzule.activityrouter.annotation.Router
import com.luck.picture.lib.tools.ScreenUtils
import com.scwang.smart.refresh.layout.api.RefreshHeader
import com.scwang.smart.refresh.layout.simple.SimpleMultiListener
import com.ybmmarket20.R
import com.ybmmarket20.adapter.FindSameGoodsAdapter
import com.ybmmarket20.bean.ITEM_TYPE_FIND_SAME_GOODS_INVALIDATED
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.common.jgTrackResourceProductClick
import com.ybmmarket20.common.splicingPageTitle2Entrance
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.AdapterUtils.updateRowsData
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.analysis.FlowData
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.viewmodel.GoodsListViewModel
import kotlinx.android.synthetic.main.activity_find_same_goods.*
import kotlinx.android.synthetic.main.common_header_items_cart.*
import kotlinx.android.synthetic.main.item_find_same_goods_invalidated.*
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 找相似
 */
@Router("findsamegoods/:skuId")
class FindSameGoodsActivity: BaseProductActivity() {

    val mViewModel: GoodsListViewModel by lazy { ViewModelProvider(this).get(GoodsListViewModel::class.java) }
    val mData: MutableList<RowsBean> = mutableListOf()
    val mAdapter = FindSameGoodsAdapter(mData, this)
    var mRequestParams: RequestParams? = null
    var mEntrance = ""
    override fun getContentViewId(): Int = R.layout.activity_find_same_goods

    override fun initData() {
        super.initData()
        val skuId = intent.getStringExtra("skuId")
        mEntrance = intent.getStringExtra(IntentCanst.JG_ENTRANCE)?:""
        XyyIoUtil.track("action_SimilarList", hashMapOf("sku_id" to skuId))
        setTitle("找相似")
//        rv.layoutManager = WrapGridLayoutManager(this, 2)
        rv.layoutManager = StaggeredGridLayoutManager(2, StaggeredGridLayoutManager.VERTICAL)
        rv.adapter = mAdapter.apply {
            jgTrackBean = JgTrackBean(
                    jgReferrer = <EMAIL>(),
                    jgReferrerTitle = "找相似",
                    jgReferrerModule = "找相似",
                    module = JGTrackManager.Common.MODULE_PRODUCT_LIST,
                    entrance = if (mEntrance.isEmpty()) "找相似" else splicingPageTitle2Entrance(
                            mEntrance,
                            "找相似"))

            resourceViewTrackListener = { rowsBean, i ->
                var productTag = ""
                rowsBean.tags?.productTags?.let { tagList ->
                    tagList.forEachIndexed { index, tagBean ->
                        if (index != tagList.size - 1) {
                            productTag += tagBean.text + "，"
                        } else {
                            productTag += tagBean.text
                        }
                    }
                }
                rowsBean.tags?.dataTags?.let { tagList ->
                    if (productTag.isNotEmpty()) {
                        productTag += ","
                    }
                    tagList.forEachIndexed { index, tagBean ->
                        if (index != tagList.size - 1) {
                            productTag += tagBean.text + "，"
                        } else {
                            productTag += tagBean.text
                        }
                    }
                }
            }

            productClickTrackListener = { rowsBean, i,number ->
                var productTag = ""
                rowsBean.tags?.productTags?.let { tagList ->
                    tagList.forEachIndexed { index, tagBean ->
                        if (index != tagList.size - 1) {
                            productTag += tagBean.text + "，"
                        } else {
                            productTag += tagBean.text
                        }
                    }
                }
                rowsBean.tags?.dataTags?.let { tagList ->
                    if (productTag.isNotEmpty()) {
                        productTag += ","
                    }
                    tagList.forEachIndexed { index, tagBean ->
                        if (index != tagList.size - 1) {
                            productTag += tagBean.text + "，"
                        } else {
                            productTag += tagBean.text
                        }
                    }
                }

                jgTrackResourceProductClick(
                        url = <EMAIL>(),
                        module = JGTrackManager.Common.MODULE_PRODUCT_LIST,
                        referrer = <EMAIL>(),
                        pageId = JGTrackManager.TrackShopCarFindSameGoods.PAGE_ID,
                        title = JGTrackManager.TrackShopCarFindSameGoods.TITLE,
                        resourceId = "",
                        resourceName = "",
                        resourceType = "",
                        position = i,
                        productId = rowsBean.productId ?: "",
                        productName = rowsBean.productName ?: "",
                        productType = "普通商品",
                        productPrice = rowsBean.jgProductPrice ?: 0.0,
                        productLabel = productTag,
                        entrance = if (jgTrackBean?.entrance?.contains(JGTrackManager.TrackShoppingCart.TITLE) == true) {
                            JGTrackManager.TrackShopCarFindSameGoods.TITLE
                        } else {
                            jgTrackBean?.entrance ?: ""
                        },
                        navigation = "")
            }
        }
//        rv.addItemDecoration(FindSameGoodsItemDecoration(this))
        mViewModel.findSameGoodsLiveData.observe(this, Observer {
            sRefresh.finishRefresh()
            mAdapter.loadMoreComplete()
            val flowData = FlowData(it?.sptype?: "", it?.spid?: "", it?.sid?: "", "", "", null)
            mAdapter.setFindSameFlowData(flowData)
            if (it?.rows == null || it.rows.isEmpty()) {
                mAdapter.loadMoreEnd(false)
                return@Observer
            }
            if (mRequestParams == null) {
                //失效商品头部
                setHeader(it.rows)
            }
            updateRowsData(it.licenseStatus, it.rows, mAdapter, mRequestParams == null, it.isEnd)
            if (mRequestParams == null) {
                lifecycleScope.launch {
                    delay(1000)
                    group_empty.visibility = if (it.rows.isEmpty()) View.VISIBLE else View.GONE
                }
            }
            mRequestParams = it.requestParams
        })
        sRefresh.setOnRefreshListener {
            mRequestParams = null
            mViewModel.getGoodsListFindSameGoods(mutableMapOf("skuId" to (skuId?: "")))
        }
        mAdapter.setOnLoadMoreListener({
            mRequestParams?.paramsMap?.let(mViewModel:: getGoodsListFindSameGoods)
        }, rv)
        sRefresh.autoRefresh()
        var preOffset = 0
        sRefresh.setOnMultiListener(object : SimpleMultiListener(){
            override fun onHeaderMoving(
                header: RefreshHeader?,
                isDragging: Boolean,
                percent: Float,
                offset: Int,
                headerHeight: Int,
                maxDragHeight: Int
            ) {
                super.onHeaderMoving(
                    header,
                    isDragging,
                    percent,
                    offset,
                    headerHeight,
                    maxDragHeight
                )
                ObjectAnimator.ofFloat(iv_empty, "translationY", preOffset.toFloat(), offset.toFloat()).start()
                ObjectAnimator.ofFloat(tv_empty, "translationY", preOffset.toFloat(), offset.toFloat()).start()
                preOffset = offset
            }
        })
    }

    /**
     * 设置头部数据
     */
    private fun setHeader(goodsRowsBeanList: MutableList<RowsBean>) {
        if (goodsRowsBeanList.isEmpty()) return
        goodsRowsBeanList[0].itemType = ITEM_TYPE_FIND_SAME_GOODS_INVALIDATED
        if (goodsRowsBeanList.size == 1) {
            val tipView = findViewById<Group>(R.id.group_title)
            tipView.visibility = View.GONE
        }
        val bean = goodsRowsBeanList[0]
        tv_goods_type.visibility = if (bean.status == 2 || bean.status == 4 || !bean.isPurchase) {
            //无效商品
            View.VISIBLE
        } else View.GONE
        goodsRowsBeanList.removeAt(0)
        ImageUtil.load(
            this,
            AppNetConfig.LORD_IMAGE + bean.imageUrl,
            findViewById(R.id.iv_invalidated_goods)
        )
        val tvInvalidatedTitle = findViewById<TextView>(R.id.tv_invalidated_title)
        // 商品名
        tvInvalidatedTitle.text = bean.productName
        // 规格/厂
        val tvInvalidatedSpec = findViewById<TextView>(R.id.tv_invalidated_spec)
        tvInvalidatedSpec.text = "${bean.spec} / ${bean.manufacturer}"
        //价格
        val tvInvalidatedPrice = findViewById<TextView>(R.id.tv_invalidated_price)
        tvInvalidatedPrice.text = bean.showPriceStr
        //零售价
        val suggestPrice = findViewById<TextView>(R.id.suggest_price)
        if (bean.showPriceType() != 0
            || (TextUtils.isEmpty(bean.suggestPrice) && TextUtils.isEmpty(bean.uniformPrice))) {
            suggestPrice.visibility = View.INVISIBLE
        } else {
            suggestPrice.visibility = View.VISIBLE
            suggestPrice.text = bean.showSuggestOrGrossMargin
        }
        val clFindSame = findViewById<ConstraintLayout>(R.id.cl_find_same)
        clFindSame.setOnClickListener {
            RoutersUtils.open("ybmpage://productdetail?${IntentCanst.PRODUCTID}=${bean.id}")
        }
    }

    override fun getRawAction(): String? = null

    class FindSameGoodsItemDecoration(val context: Context?): RecyclerView.ItemDecoration() {

        val dp = ScreenUtils.dip2px(context, 1f)

        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            val childLayoutPosition = parent.getChildLayoutPosition(view)
            if (childLayoutPosition % 2 == 0) {
                outRect.set(8*dp, 6*dp, 3*dp, 0)
            } else {
                outRect.set(3*dp, 6*dp, 8*dp, 0)
            }
        }
    }
}