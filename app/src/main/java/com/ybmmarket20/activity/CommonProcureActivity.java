package com.ybmmarket20.activity;

import android.content.Intent;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.GoodsListAdapter;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.bean.RowsListBean;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.SpUtil;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;

/**
 * 常用采购
 */
@Router("commonprocureactivity")
public class CommonProcureActivity extends BaseProductActivity {

    @Bind(R.id.list_lv)
    CommonRecyclerView commonLv;

    private List<RowsBean> rows;
    private int pageSize = 10;
    private int pager = 0;
    private String mEmptyStr;

    @Override
    protected void initData() {
        super.initData();
        setTitle("常用采购");
        mEmptyStr = "单上空空?马上去采购商品吧!";
        detailAdapter = new GoodsListAdapter(R.layout.item_goods, rows, false, false);
        detailAdapter.setEmptyView(this,R.layout.layout_empty_view, R.drawable.icon_empty, mEmptyStr);
        commonLv.setEnabled(true);
        commonLv.setAdapter(detailAdapter);
        detailAdapter.setEnableLoadMore(true);
        commonLv.setListener(new CommonRecyclerView.Listener() {
            @Override
            public void onRefresh() {
                getLoadMoreResponse(0);
            }

            @Override
            public void onLoadMore() {
                getLoadMoreResponse(pager);
            }
        });
        detailAdapter.setOnListItemClickListener(new GoodsListAdapter.OnListViewItemClickListener() {
            @Override
            public void onItemClick(RowsBean rows) {
                if (rows != null) {
                    Intent intent = new Intent(CommonProcureActivity.this, ProductDetailActivity.class);
                    intent.putExtra(IntentCanst.PRODUCTID, rows.getId() + "");
                    startActivity(intent);
                }
            }
        });
    }

    @Override
    protected String getRawAction() {
        return "ybmpage://commonprocureactivity/";
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_common_procure;
    }

    /**
     * 获取常用采购列表
     *
     * @param page 分页
     *             常用采购 url => "orders/common"
     */
    public void getLoadMoreResponse(final int page) {
        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);
        params.put("limit", String.valueOf(pageSize));
        params.put("offset", String.valueOf(String.valueOf(page)));
        HttpManager.getInstance().post(AppNetConfig.COMMON_PROCURE, params, new BaseResponse<RowsListBean>() {

            @Override
            public void onSuccess(String content, BaseBean<RowsListBean> brandBean, RowsListBean rowsBeans) {
                completion();
                if (brandBean != null) {
                    if (brandBean.isSuccess()) {
                        detailAdapter.setFlowData(mFlowData);
                        if (rowsBeans != null) {

                            if (rowsBeans.rows != null && rowsBeans.rows.size() > 0) {
                                if (page <= 0) {
                                    CommonProcureActivity.this.pager = 1;
                                } else {
                                    CommonProcureActivity.this.pager++;
                                }
                            }
                            if (page <= 0) {

                                if (rows == null) {
                                    rows = new ArrayList<>();
                                }

                                if (rows.size() <= 0 && rowsBeans.rows != null) {
                                    rows.addAll(rowsBeans.rows);
                                } else {
                                    if (rowsBeans.rows == null || rowsBeans.rows.isEmpty()) {

                                    } else {
                                        for (RowsBean bean : rowsBeans.rows) {
                                            if (rows.contains(bean)) {
                                                rows.remove(bean);
                                            }
                                        }
                                        rows.addAll(0, rowsBeans.rows);
                                    }
                                }
                                detailAdapter.setNewData(rows);
                                detailAdapter.notifyDataChangedAfterLoadMore(rows.size() >= pageSize);
                            } else {
                                if (rowsBeans.rows == null || rowsBeans.rows.size() <= 0) {
                                    detailAdapter.notifyDataChangedAfterLoadMore(false);
                                } else {

                                    for (RowsBean bean : rowsBeans.rows) {
                                        if (rows.contains(bean)) {
                                            rows.remove(bean);
                                        }
                                    }
                                    rows.addAll(rowsBeans.rows);
                                    detailAdapter.setNewData(rows);
                                    detailAdapter.notifyDataChangedAfterLoadMore(rowsBeans.rows.size() >= pageSize);
                                }
                            }
                        }
                    } else {
                        detailAdapter.setNewData(rows);
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                completion();
                if (commonLv != null) {
                    commonLv.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                if (commonLv != null) {
                                    detailAdapter.setNewData(rows);
                                }
                            } catch (Throwable e) {
                                BugUtil.sendBug(e);
                            }
                        }
                    }, 300);
                }
            }
        });
    }

    private void completion() {
        if (commonLv != null) {
            try {
                commonLv.setRefreshing(false);
            } catch (Throwable e) {
                BugUtil.sendBug(e);
            }
        }
    }

}
