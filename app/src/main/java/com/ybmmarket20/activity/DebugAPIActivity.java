package com.ybmmarket20.activity;

import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.DebugAPIBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.DebugManager;
import com.ybmmarket20.view.DebugPopWindow;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;

public class DebugAPIActivity extends BaseActivity {

    @Bind(R.id.list_view)
    CommonRecyclerView listView;
    private List<DebugAPIBean> list = new ArrayList<>();
    private BaseQuickAdapter baseQuickAdapter;
    private DebugPopWindow debugPopWindow;

    private void initView() {
        list = DebugManager.getInstance().getApis();
        baseQuickAdapter = new BaseQuickAdapter<DebugAPIBean, BaseViewHolder>(R.layout.debug_list_item, list) {
            @Override
            protected void convert(BaseViewHolder baseViewHolder, DebugAPIBean bean) {
                baseViewHolder
                        .setText(R.id.url, "地址:" + bean.url)
                        .setText(R.id.param, "参数:" + bean.params)
                        .setText(R.id.response, "返回:" + bean.response)
                        .setText(R.id.time, "");
            }
        };
        listView.setAdapter(baseQuickAdapter);
        baseQuickAdapter.setEnableLoadMore(false);
        baseQuickAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter baseQuickAdapter, View view, int position) {
                DebugAPIBean bean = list.get(position);
                if (bean == null || bean.response == null) {
                    return;
                } else {
                    if (debugPopWindow == null) {
                        debugPopWindow = new DebugPopWindow();
                    }
                    debugPopWindow.show(listView, bean);
                }
            }
        });
        listView.setEnabled(false);
        listView.setShowAutoRefresh(false);
    }

    @Override
    protected void initData() {
        setTitle("网络接口分析");
        initView();
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_debug_api;
    }


}
