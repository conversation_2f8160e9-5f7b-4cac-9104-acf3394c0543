package com.ybmmarket20.activity;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.fragments.AddImage2Fragment;
import com.ybmmarket20.fragments.AddImageFragment;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.ButtonObserver;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;
import butterknife.OnClick;

/*
 * 上传图片采购单详情
 * */
@Router({"uploadimagecartdetail"})
public class UploadImageCartDetailActivity extends BaseActivity {

    @Bind(R.id.iv_back)
    ImageView ivBack;
    @Bind(R.id.tv_title)
    TextView tvTitle;
    @Bind(R.id.tv_right)
    TextView tvRight;
    @Bind(R.id.iv_right)
    ImageView ivRight;
    @Bind(R.id.ll_create_list)
    LinearLayout llCreateList;
    @Bind(R.id.ll_title)
    RelativeLayout llTitle;
    @Bind(R.id.et_name)
    EditText etName;
    @Bind(R.id.fragment)
    RelativeLayout fragment;
    @Bind(R.id.btn_ok)
    ButtonObserver btnOk;

    protected AddImage2Fragment imageFragment;
    protected Bundle arg;

    @Override
    protected void initData() {
        setTitle("上传采购单图片");

        arg = AddImageFragment.getBundle2Me(9, true, false, true);
        arg.putBoolean("allowe_add", true);

        imageFragment = new AddImage2Fragment();
        imageFragment.setArguments(arg);
        getSupportFragmentManager().beginTransaction()
                .replace(R.id.fragment, imageFragment).commit();

        btnOk.observer(etName);
        btnOk.setOnItemClickListener(new ButtonObserver.OnButtonObserverListener() {
            @Override
            public void onButtonObserver(boolean isFlag) {
                if (isFlag) {
                    setButtonStyle(R.drawable.bg_btn_upload_cart_detail, UiUtils.getColor(R.color.white));
                } else {
                    setButtonStyle(R.drawable.bg_btn_upload_cart_detail2, UiUtils.getColor(R.color.white));
                }
            }
        });
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_upload_image_cart_detail;
    }

    @OnClick({R.id.btn_ok})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.btn_ok:
                applyRefund();
                break;
        }
    }

    private void applyRefund() {
        if (btnOk == null) {
            return;
        }
        if (imageFragment.confirm()) {

            String name = etName.getText().toString().trim();
            if (TextUtils.isEmpty(name)) {
                ToastUtils.showShort("请输入图片采购单名称");
                return;
            }

            List<String> list = imageFragment.getFileNameList();
            if (list == null) {
                list = new ArrayList<>();
            }
            if (list != null && list.size() <= 0) {
                ToastUtils.showShort("请选择上传的图片");
                return;
            }
            btnOk.setEnabled(false);
            StringBuffer sb = new StringBuffer();
            for (String s : list) {
                sb.append(s).append(";");
            }
            if (sb.length() > 0) {
                sb.deleteCharAt(sb.length() - 1);
            }

            showProgress();
            RequestParams params = new RequestParams();
            String merchantid = SpUtil.getMerchantid();
            params.put("merchantId", merchantid);//商户ID
            params.put("purchaseName", name);
            params.put("picUrls", sb.toString());

            HttpManager.getInstance().post(AppNetConfig.PICTURE_ADD, params, new BaseResponse<EmptyBean>() {

                @Override
                public void onSuccess(String content, BaseBean<EmptyBean> data, EmptyBean baseBean) {
                    dismissProgress();
                    if (btnOk == null) {
                        return;
                    }
                    btnOk.setEnabled(true);
                    if (data != null && data.isSuccess()) {
                        if (!TextUtils.isEmpty(data.msg)) {
                            ToastUtils.showShort(data.msg);
                        }

                        finish();
                    }
                }

                @Override
                public void onFailure(NetError error) {
                    dismissProgress();
                    btnOk.setEnabled(true);
                    super.onFailure(error);
                }
            });
        }

    }

    /**
     * 设置登录按钮风格
     *
     * @param drawableStyle 背景样式
     * @param colorStyle    文字颜色
     */
    public void setButtonStyle(int drawableStyle, int colorStyle) {
        if (btnOk == null) {
            return;
        }
        btnOk.setBackgroundResource(drawableStyle);
        btnOk.setTextColor(colorStyle);
    }

}
