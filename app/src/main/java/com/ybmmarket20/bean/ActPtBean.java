package com.ybmmarket20.bean;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ybmmarket20.bean.homesteady.SpellGroupActRangePrice;

import java.util.ArrayList;
import java.util.List;

/**
 * 拼单
 */
@Keep
public class ActPtBean extends SpellGroupActRangePrice {

    public int assembleStatus;      //0未开始 1进行中 2结束
    public Double assemblePrice;    //拼团价==商品中的fob价格
    public long assembleStartTime;  //拼团开始时间, 单位ms
    public long assembleEndTime;    //拼团结束时间, 单位ms
    public double percentage;       //拼团进度   10% 则返回 10
    public int skuStartNum;         //起拼数量
    public int orderNum;            //已拼数量
    public long surplusTime;        //拼团剩余持续时间, 单位s
    public long responseLocalTime;  //获取到拼团信息的本地时间, 单位ms
    public String subTitle;         //拼团轮播显示的title信息
    public String subTitleNum;         //已参与拼团客户数
    public String marketingId;      //拼团活动id
    public List<AssembleOrderList> assembleOrderList;//拼团轮播信息
    public int preheatShowPrice;    //拼团预热期是否展示价格字段,0不展示，1展示
    public String merchantCount;//购买人数
    public String merchantCountDesc;//购买人数描述
    public boolean supportSuiXinPin; //是否是随心拼商品
    public String suiXinPinButtonText; //随心拼按钮文案
    public String suiXinPinButtonBubbleText; //随心拼按钮气泡文案

    public boolean isApplyListShowType; // 是否使用在列表页上弹出动态面板的操作方式，boolean类型，必填


    public ActPtBean(int stepPriceStatus, @Nullable String minSkuPrice, @Nullable String maxSkuPrice, @Nullable String startingPriceShowText, @Nullable String rangePriceShowText, @NonNull List<String> stepPriceShowTexts) {
        super(stepPriceStatus, minSkuPrice, maxSkuPrice, startingPriceShowText, rangePriceShowText, stepPriceShowTexts);
    }

    public ActPtBean(){
        super(2, null, null, null, null, new ArrayList<>());
    }

    public String getOrderNumStr(){
        if (orderNum < 10000) {
            return String.valueOf(orderNum);
        } else {
            double orderNumInTenThousand = orderNum / 10000.0;
            return String.format("%.1f万", orderNumInTenThousand);
        }
    }
}
