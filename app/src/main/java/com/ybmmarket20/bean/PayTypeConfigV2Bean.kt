package com.ybmmarket20.bean

import com.ybmmarket20.bean.payment.VirtualGoldRechargeTextBean

data class PayTypeConfigV2Bean(
        var payTypeList: MutableList<PayTypeEntry>?,
        var payEndTime: String?,
        var tips: String?, //温馨提示
        var isChannelOrder: String?, //1渠道订单,0订单详情
        var money: String?, //付款金额
        var countDownTime: Long?,
        var rechargeTextList: ArrayList<VirtualGoldRechargeTextBean>?,
        var resultTextList: ArrayList<VirtualGoldRechargeTextBean>?,
        var resultType: String?
) {

    var payDiscountTips: String? = null


    companion object {
        fun fromPayConfig(payConfigBean: PayConfigBean): PayTypeConfigV2Bean {
            return PayTypeConfigV2Bean(
                    null,
                    payConfigBean.payEndTime,
                    payConfigBean.tips,
                    payConfigBean.isChannelOrder,
                    payConfigBean.money,
                    payConfigBean.countDownTime,
                    payConfigBean.rechargeTextList,
                    payConfigBean.resultTextList,
                    payConfigBean.resultType
            ).apply {
                payDiscountTips = payConfigBean.payDiscountTips
            }
        }
    }
}