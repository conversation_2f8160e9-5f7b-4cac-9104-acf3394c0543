package com.ybmmarket20.bean;


import java.util.List;

public class GiftSkulistBean {

    public int id;
    public List<GiftSkulist> giftSkulist;
    public int giftId;//id
    public String title;//标题
    public long startTime;//开始时间
    public long endTime;//结束时间
    public int status;//状态，1-已领取，2-已使用，3-已过期，4-已拒绝
    public String appUrl;//点击跳转
    public double orderAmout;//金额
    public double useAmount;//金额

    public boolean isStatus() {
        return status == 1;
    }

//    @Override
//    public boolean equals(Object o) {
//        if (this == o) return true;
//        if (o == null || getClass() != o.getClass()) return false;
//
//        GiftSkulistBean rowsBean = (GiftSkulistBean) o;
//
//        if (giftId != rowsBean.giftId) return false;
//        return title != null ? title.equals(rowsBean.title) : rowsBean.title == null;
//    }

        @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        GiftSkulistBean rowsBean = (GiftSkulistBean) o;

        return (giftId != rowsBean.giftId);
    }

    @Override
    public int hashCode() {
        int result = giftId;
        result = 31 * result + (title != null ? title.hashCode() : 0);
        return result;
    }


}
