package com.ybmmarket20.bean;

/**
 * 药口令功能
 */
public class YbmCommandBean {

    public String imageUrl;
    public String commonName;
    public String spec;
    public String fob;
    public int commandType;
    public String detailUrl;
    public boolean isOEM;//true是OEM协议商品，为空或者false为非OEM协议商品(此处设置为String类型是因为web页面boolean类型不支持判断是否存在,而为了兼容之前没有OEM需求的情况又需要判断), true/false
    public int signStatus;//协议签署状态(0-未签署,1-已签署)是普通商品或者(OEM协议商品且已签署协议)才会显示价格

}
