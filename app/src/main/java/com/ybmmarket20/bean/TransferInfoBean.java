package com.ybmmarket20.bean;

public class TransferInfoBean {

    /**
     * "bankAccount": "4205 0126 6939 0000 0015",
     * 			"branchCode": "XS420000",
     * 			"bankInfo": "中国建设银行武汉吴南支行",
     * 			"isThirdCompany": "0",
     * 			"companyInfo": "武汉小药药医药科技有限公司",
     * 			"explainTwo": "从下单之日起5天内如果还未付款并到账，系统将自动取消该订单。",
     * 			"explainThree": "我们将在收到款项后及时确认收款并安排发货，若款项汇出2天后未帮您收款确认，请及时拨打底部客服热线",
     * 			"explainZero": "请您在汇款时备注药帮忙订单编号，这将会很大程度上缩短我们的核款时间并能尽快为您安排发货。",
     * 			"transferInfoUrl": "ybmpage://commonh5activity?url=https://app-v4.ybm100.com/static/xyyvue/dist/#/onlinepay?ybm_title=转账信息&orderId=2548260",
     * 			"explainOne": "请于24小时内汇款并确保汇款金额与订单总金额一致，到账时间为1-3个工作日。"
     */
    public String transferInfoUrl;
}
