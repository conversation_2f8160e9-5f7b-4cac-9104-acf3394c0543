package com.ybmmarket20.bean;

import java.util.List;

/**
 * 登录
 */
public class Login  {
    public String merchantId;//商户id,唯一标示
    public String token;//商户id,唯一标示
    public int state;//是否已经输入邀请码 //3需要激活（已作废）

    public String realName;
    public String provinceCode;
    public String province;
    public String cityCode;
    public String city;
    public String areaCode;
    public String district;
    public String activeTime;
    public String channelNames;
    public int licenseStatus; //资质一审状态
    public boolean isKa;

    public int validity;            // 资质提醒 0：正常，1：过期，2：临期
    public boolean isCrawler;       // 是否是爬虫账号

    /*
    "realName": "浙江测试药店",
    "areaCode": 350825,
    "province": "福建省",
    "merchantId": 110204,
    "city": "龙岩市",
    "provinceCode": 350000,
    "cityCode": 350800,
    "district": "连城县",
    "activeTime": 1548420329000,
    "state": 1,
    "token": "1x6Ay09yDA_A2aB8p7Bp41_28@10!85~AD*A8%CC<C9:A3.F"
    */



    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }
}
