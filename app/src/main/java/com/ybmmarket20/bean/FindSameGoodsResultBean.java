package com.ybmmarket20.bean;

import androidx.annotation.Keep;

import java.util.List;

/**
 *
 * Created by asus on 2018/1/19.
 */

@Keep
public class FindSameGoodsResultBean extends BaseFeedBean<RowsBean> {

    public List<RowsBean> rows;
    public int count;

    // 1 为默认，什么都没有触发
    // 2、3 为触发人工关键词映射
    // 4 为触发包含停止次
    public String keyWordHitType;

    public String keyWord;//原始词

    public String keyWordHitWord;//	命中词，即更改后的词

    // 319 等活动筛选条件
    public String activityScreenTagId;
    public String selectActivityScreenTagTitle;     // 选中活动筛选标题
    public String unSelectActivityScreenTagTitle;   // 未选中活动筛选标题

    public int type;

    public List<String> wordList;

    @Override
    public List<RowsBean> getDataList() {
        return null;
    }

}
