package com.ybm.activity.ad

import android.util.Log
import com.ybm.activity.ad.AdFragment.Companion.AD_SCM_STR
import com.ybm.activity.ad.AdFragment.Companion.AD_SPM_STR
import com.ybmmarket20.common.BaseFragment2
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.page.ad.AdReportEvent
import com.ybmmarket20.xyyreport.spm.SpmBean
import com.ybmmarket20.xyyreport.spm.SpmUtil

abstract class AdAnalysisFragment: BaseFragment2() {

    private var mAdSpmStr: String? = ""
    private var mAdScmStr: String? = ""
    private var mSpmCtn: SpmBean? = null

    override fun initData(content: String?) {
        try {
            mAdSpmStr = arguments?.getString(AD_SPM_STR)?: ""
            mAdScmStr = arguments?.getString(AD_SCM_STR)?: ""
            Log.i("trackAdPv", mAdSpmStr + " " + mAdScmStr)
            trackAdPv()
            trackAdComponentExposure()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun trackAdPv() {
        SpmLogUtil.print("广告spm" + mAdSpmStr)
        mSpmCtn = SpmUtil.getSpmPvFromStr(mAdSpmStr)
        SpmUtil.setNewSpmE(mSpmCtn)
        AdReportEvent.trackAdPv(requireActivity(), mSpmCtn)
    }

    private fun trackAdComponentExposure(){
        AdReportEvent.trackAdComponentExposure(requireActivity(), mAdSpmStr, mSpmCtn)
    }

    fun trackAdSubModuleClick() {
        AdReportEvent.trackAdSubModuleClick(requireActivity(), mAdSpmStr, mAdScmStr, mSpmCtn)
    }


}