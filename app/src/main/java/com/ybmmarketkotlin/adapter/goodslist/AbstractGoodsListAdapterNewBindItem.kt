package com.ybmmarketkotlin.adapter.goodslist

import android.content.Context
import android.os.CountDownTimer
import android.text.TextUtils
import android.util.SparseArray
import android.view.View
import android.widget.*
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.*
import com.ybmmarket20.common.*
import com.ybmmarket20.common.JGTrackManager.Companion.getSuperProperty
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.common.widget.RoundTextView
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.report.coupon.ICouponEntryType
import com.ybmmarket20.reportBean.AddToCart
import com.ybmmarket20.reportBean.JGPageListCommonBean
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.*
import com.ybmmarket20.view.ProductEditLayoutNew
import com.ybmmarket20.view.ProductEditLayoutNew.BUTTON_TYPE_ADD
import com.ybmmarket20.view.ProductEditLayoutNew.BUTTON_TYPE_INPUT
import com.ybmmarket20.view.PromotionTagView
import com.ybmmarket20.view.ShopNameWithTagView
import com.ybmmarket20.view.ShowPromotionPopWindowNew
import com.ybmmarket20.xyyreport.page.search.SearchProductReport
import com.ybmmarket20.xyyreport.spm.SpmUtil
import com.ybmmarketkotlin.utils.TextWithPrefixTag
import com.ybmmarketkotlin.utils.tagStyle
import com.ybmmarketkotlin.views.SeckillTimeView

abstract class AbstractGoodsListAdapterNewBindItem(
    val mContext: Context,
    val baseViewHolder: YBMBaseHolder,
    val rowsBean: RowsBean,
    private val countDownTimerMap: SparseArray<CountDownTimer>,
    private val isAddCartShowPopupWindow: Boolean = false,
    private val isHiddenPromotionMore: Boolean = false,
) : IGoodsListAdapterNewBindItem {

    companion object {
        // 单价相关常量
        private const val UNIT_PRICE_SEPARATOR = "| "
        private const val UNIT_PRICE_MARGIN_DP = R.dimen.dimen_dp_8
    }

    var mFlowData: BaseFlowData? = null  //埋点数据
    var addCartPopWindowCallback: ((rowsBean: RowsBean) -> Unit)? = null
    var newTrackParams: NewTrackParams? = null
    var jgTrackBean: JgTrackBean? = null
    var isFromFrequetly = false
    //是否来自搜索页的sug
    var isFromSearchSug = false
    var productClickTrackListener: ((RowsBean, Int, isBtnClick: Boolean, btnContent: String, number: Int?) -> Unit)? =
        null
    var jgPageListCommonBean: JGPageListCommonBean? = null
    var mCouponEntryType: ICouponEntryType? = null
    var isFromShopCartGatherOrders: Boolean = false

    fun setOnAddCartPopupWindowCallback(addCartPopWindowCallback: ((rowsBean: RowsBean) -> Unit)?) {
        this.addCartPopWindowCallback = addCartPopWindowCallback
    }

    override fun onInitViewVisibility() {
        initViewVisibility(baseViewHolder)
    }

    override fun onLoadGoodsIcon() {
        ImageUtil.load(
            mContext,
            AppNetConfig.LORD_IMAGE + rowsBean.imageUrl,
            baseViewHolder.getView(R.id.icon)
        )
    }

    override fun onHandleSellingPointTag() {
        val ivMark: ImageView = baseViewHolder.getView(R.id.iv_shop_mark)
        if (!rowsBean.markerUrl.isNullOrEmpty()) {
            ivMark.visibility = View.VISIBLE
            ImageUtil.load(mContext, AppNetConfig.LORD_TAG + rowsBean.markerUrl, ivMark)
        } else {
            ivMark.visibility = View.GONE
        }
    }

    override fun onGoodsMedicalInsuranceWithTags() {
        // 将标签添加到列表
        val tagList = mutableListOf<TagBean>()
        rowsBean.tags?.productMainTagList?.let { tagList.addAll(it) };
        val hsvScroll: ScrollView = baseViewHolder.getView(R.id.hsv_data_tags)
        if(tagList.size == 0){
            hsvScroll.visibility = View.GONE;
            return;
        }else{
            hsvScroll.visibility = View.VISIBLE;
        }

        val mITagsView: LinearLayout = baseViewHolder.getView(R.id.ll_data_tags_container)
        // 创建ShopNameWithTagView
        val shopNameWithTagView = ShopNameWithTagView(mContext)

        // 设置标签数据
        shopNameWithTagView.bindData(tagList, maxTagCount = 100)

        // 设置布局参数
        val layoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        )
        
        shopNameWithTagView.layoutParams = layoutParams

        // 清除容器并添加新视图
        mITagsView.removeAllViews()
        mITagsView.addView(shopNameWithTagView)
        mITagsView.visibility = View.VISIBLE
    }

    override fun onHandlePreHotTag() {
        // 标签2 预热标签
        val mPtv: PromotionTagView = baseViewHolder.getView(R.id.view_ptv)
        if (rowsBean.tags?.activityTag != null) {
            mPtv.visibility = View.VISIBLE
            mPtv.setShowData(rowsBean.tags?.activityTag)
        } else {
            mPtv.visibility = View.GONE
        }
    }

    override fun onBuyCount() {
        // 标签3 买过多少次
        val tvBuyCount = baseViewHolder.getView<TextView>(R.id.rtv_buy_count)
        tvBuyCount.visibility = View.GONE
        if(isFromFrequetly){
            // //常购常搜
            rowsBean.tags?.userPurchasedSearchedTag?.let {
                tvBuyCount.visibility = View.VISIBLE
                tvBuyCount.tagStyle(it)
            }
        }else{
            rowsBean.tags?.purchaseTags?.takeIf { it.size > 0 }?.let {
                tvBuyCount.visibility = View.VISIBLE
                tvBuyCount.tagStyle(it[0])
            }
        }
    }

    override fun onGoodsNameWithTags() {
        // 标签4 商品标签 + 商品名称
        val goodsName = baseViewHolder.getView<TextView>(R.id.shop_name)
        //搜索的sug类型中已经下架了商品类型的（这个高亮代码先保留  需要再放开，但还没测，需要测一下）
//        if (isFromSearchSug){ //搜索的sug需要高亮
//            goodsName.TextWithPrefixTagContentSpannable(rowsBean.tags?.titleTags,highLightText(rowsBean.productName,rowsBean.searchKeyword?:"",ContextCompat.getColor(mContext,R.color.color_00b955)))
//        }else{
//            goodsName.TextWithPrefixTag(rowsBean.tags?.titleTags, rowsBean.productName)
//        }
        goodsName.TextWithPrefixTag(rowsBean.tags?.titleTags, rowsBean.productName)
        goodsName.setLineSpacing(0f, 1.1f)
    }

    override fun onGoodsSpec() {
        // 规格
        if (TextUtils.isEmpty(rowsBean.spec)) {
            baseViewHolder.setGone(R.id.tv_goods_spec, false)
            baseViewHolder.setGone(R.id.iv_divider_of_spec_name, false)
        } else {
            baseViewHolder.setGone(R.id.tv_goods_spec, true)
            baseViewHolder.setGone(R.id.iv_divider_of_spec_name, true)
            baseViewHolder.setText(R.id.tv_goods_spec, rowsBean.spec)
        }
    }

    override fun onManufacturerTag() {
        // 厂
        baseViewHolder.setText(R.id.tv_chang_name, rowsBean.manufacturer)
    }

    override fun onEffectTags() {
        if (TextUtils.isEmpty(rowsBean.nearEffect)) {
            baseViewHolder.setGone(R.id.tv_validity_period, false)
        } else {
            baseViewHolder.setGone(R.id.tv_validity_period, true)
            baseViewHolder.setText(
                R.id.tv_validity_period,
                "有效期:${rowsBean.nearEffect}"
            )
        }
//        baseViewHolder.getView<TextView?>(R.id.tv_validity_period)?.visibility = View.VISIBLE
    }

    override fun onDataTags() {
        // 标签5 数据标签 ： "60天最低价",8, "区域毛利榜" 10, "比上次购买时降xx元",11, "比加入时降xx元",12, "品类点击榜"
        val tag5: ShopNameWithTagView = baseViewHolder.getView(R.id.data_tag_list_view)
//        if (rowsBean.tags?.dataTags == null || rowsBean.tags?.dataTags?.isEmpty() == true) {
//            tag5.visibility = View.GONE
//        } else {
//            tag5.visibility = View.VISIBLE
//            tag5.bindData(rowsBean.tags?.dataTags)
//        }
        tag5.visibility = View.GONE
    }

    override fun onGoodsPrice(showUnderlinePrice: Boolean, showPgbyUnderLineProce: Boolean) {
        // 增加价格展示逻辑, 包括控销、签署协议、是否符合协议标准展示价格
        if (rowsBean.rangePriceBean.isStep()) {
            //阶梯价
            baseViewHolder.setText(
                R.id.shop_price,
                rowsBean.rangePriceBean.getDoubleStepSpannableForGoodsList()
            )
        } else {
            baseViewHolder.setText(R.id.shop_price, rowsBean.getShowPriceStrNew(showUnderlinePrice))
        }
        baseViewHolder.setVisible(R.id.ll_product_price, true)
    }

    override fun onGoodsDiscountPrice() {
        // 折后价
        val tv_original_price = baseViewHolder.getView<TextView>(R.id.tv_original_price)

        if ((!rowsBean.isControlTitle || rowsBean.controlType == 5) && rowsBean.availableQty > 0 && !rowsBean.showPriceAfterDiscount.isNullOrEmpty()) {
            tv_original_price.visibility = View.VISIBLE
            tv_original_price.text = rowsBean.showPriceAfterDiscount
        } else {
            tv_original_price.visibility = View.GONE
        }
    }

    override fun onRetailPrice() {
        // 控销价或零售价 毛利
        val tvRetailPrice: TextView = baseViewHolder.getView(R.id.tv_retail_price)
        //如果建议零售价存在或者是控销并且不可购买，控销不显示
        if (rowsBean.showPriceType() != 0
            || (TextUtils.isEmpty(rowsBean.suggestPrice)
                    && TextUtils.isEmpty(rowsBean.uniformPrice))
        ) {
            tvRetailPrice.visibility = View.INVISIBLE
        } else {
            tvRetailPrice.visibility = View.VISIBLE
            tvRetailPrice.text = rowsBean.showSuggestOrGrossMargin
        }
    }

    /**
     * 处理单价显示 - 通用方法
     */
    protected fun handleUnitPrice() {
        if (!hasUnitPriceData()) {
            hideAllUnitPriceViews()
            return
        }

        if (isFindSameGoodsLayout()) {
            handleFindSameGoodsUnitPrice()
        } else {
            handleNormalUnitPrice()
        }
    }

    /**
     * 检查是否有单价数据
     */
    private fun hasUnitPriceData(): Boolean = !TextUtils.isEmpty(rowsBean.unitPrice)

    /**
     * 隐藏所有单价相关视图
     */
    private fun hideAllUnitPriceViews() {
        safeGetView<TextView>(R.id.tv_unit_price)?.visibility = View.GONE
        safeGetView<TextView>(R.id.tv_unit_price_groupbooking)?.visibility = View.GONE
    }

    /**
     * 安全获取视图，避免异常
     */
    private inline fun <reified T : View> safeGetView(viewId: Int): T? {
        return try {
            baseViewHolder.getView<T>(viewId)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 处理 FindSameGoods 布局的单价显示
     */
    private fun handleFindSameGoodsUnitPrice() {
        val tvUnitPrice = baseViewHolder.getView<TextView>(R.id.tv_unit_price)
        val tvUnitPriceGroupbooking = safeGetView<TextView>(R.id.tv_unit_price_groupbooking)
        val clGroupbooking = baseViewHolder.getView<View>(R.id.cl_groupbooking)

        if (isGroupbookingVisible(clGroupbooking) && tvUnitPriceGroupbooking != null) {
            showGroupbookingUnitPrice(tvUnitPriceGroupbooking, tvUnitPrice)
        } else {
            showNormalUnitPrice(tvUnitPrice, tvUnitPriceGroupbooking, clGroupbooking)
        }
    }

    /**
     * 处理普通布局的单价显示
     */
    private fun handleNormalUnitPrice() {
        val tvUnitPrice = baseViewHolder.getView<TextView>(R.id.tv_unit_price)
        configureUnitPriceView(tvUnitPrice)
    }

    /**
     * 检查拼团组件是否可见
     */
    private fun isGroupbookingVisible(clGroupbooking: View): Boolean =
        clGroupbooking.visibility == View.VISIBLE

    /**
     * 显示拼团商品的单价
     */
    private fun showGroupbookingUnitPrice(tvUnitPriceGroupbooking: TextView, tvUnitPrice: TextView) {
        tvUnitPriceGroupbooking.apply {
            visibility = View.VISIBLE
            text = "$UNIT_PRICE_SEPARATOR${rowsBean.unitPrice}"
        }
        tvUnitPrice.visibility = View.GONE
    }

    /**
     * 显示普通商品的单价
     */
    private fun showNormalUnitPrice(
        tvUnitPrice: TextView,
        tvUnitPriceGroupbooking: TextView?,
        clGroupbooking: View
    ) {
        configureUnitPriceView(tvUnitPrice)
        tvUnitPriceGroupbooking?.visibility = View.GONE
        adjustFindSameGoodsUnitPriceConstraints(tvUnitPrice, clGroupbooking)
    }

    /**
     * 配置单价视图的基本属性
     */
    private fun configureUnitPriceView(tvUnitPrice: TextView) {
        tvUnitPrice.apply {
            visibility = View.VISIBLE
            text = rowsBean.unitPrice
            setBackgroundColor(ContextCompat.getColor(mContext, R.color.color_F5F5F5))
        }
    }

    /**
     * 动态调整单价位置
     * 规则：tv_spell_time → tv_unit_price → tv_groupbooking
     */
    protected fun adjustUnitPricePosition(tvUnitPrice: TextView) {
        if (isFindSameGoodsLayout()) {
            adjustFindSameGoodsUnitPricePosition(tvUnitPrice)
        } else {
            adjustNormalUnitPricePosition(tvUnitPrice)
        }
    }

    /**
     * 检查是否是 FindSameGoods 布局
     */
    private fun isFindSameGoodsLayout(): Boolean = safeGetView<View>(R.id.cl_groupbooking) != null

    /**
     * 调整 FindSameGoods 布局中的单价约束，确保不被标签行挡住
     */
    private fun adjustFindSameGoodsUnitPriceConstraints(tvUnitPrice: TextView, clGroupbooking: View) {
        updateConstraintLayoutParams(tvUnitPrice) { params ->
            // 清除现有的垂直约束
            params.topToBottom = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET

            // 根据拼团组件的可见性设置约束
            params.topToBottom = if (clGroupbooking.visibility == View.VISIBLE) {
                R.id.cl_groupbooking
            } else {
                R.id.ll_product_price
            }
        }
    }

    /**
     * 调整 FindSameGoods 布局中的单价位置
     */
    private fun adjustFindSameGoodsUnitPricePosition(tvUnitPrice: TextView) {
        val clGroupbooking = baseViewHolder.getView<View>(R.id.cl_groupbooking)
        adjustFindSameGoodsUnitPriceConstraints(tvUnitPrice, clGroupbooking)
    }

    /**
     * 调整普通布局中的单价位置
     */
    private fun adjustNormalUnitPricePosition(tvUnitPrice: TextView) {
        val tvSpellTime = safeGetView<TextView>(R.id.tv_spell_time) ?: return

        updateConstraintLayoutParams(tvUnitPrice) { params ->
            // 清除现有的水平约束
            params.startToEnd = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
            params.startToStart = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET

            // 根据 tv_spell_time 的可见性设置约束
            if (tvSpellTime.visibility == View.VISIBLE) {
                params.startToEnd = R.id.tv_spell_time
                params.marginStart = mContext.resources.getDimensionPixelSize(UNIT_PRICE_MARGIN_DP)
            } else {
                params.startToStart = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
                params.marginStart = 0
            }
        }
    }

    /**
     * 更新约束布局参数的通用方法
     */
    private fun updateConstraintLayoutParams(
        view: TextView,
        updateAction: (androidx.constraintlayout.widget.ConstraintLayout.LayoutParams) -> Unit
    ) {
        val layoutParams = view.layoutParams as? androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
        layoutParams?.let { params ->
            updateAction(params)
            view.layoutParams = params
        }
    }

    override fun onMediumPackage() {
        val tvMediumPackageTitle = baseViewHolder.getView<TextView>(R.id.shop_price_tv)
        tvMediumPackageTitle.text = rowsBean.mediumPackageTitle
        if ((rowsBean.status == 2 || rowsBean.status == 4 || rowsBean.availableQty <= 0)) {
            tvMediumPackageTitle.visibility = View.GONE
        } else {
            tvMediumPackageTitle.visibility = View.VISIBLE
        }
    }

    /**
     * 营销标签
     */
    override fun onMarketingTags() {
        val tag7: ShopNameWithTagView = baseViewHolder.getView(R.id.rl_icon_type)
        val tagList = getAllTags()
        if (tagList.isNotEmpty()) {
            tag7.visibility = View.VISIBLE
        } else {
            tag7.visibility = View.GONE
        }
        tag7.bindData(tagList, maxTagCount = 100)
        // 下拉弹窗shop_name
        tag7.setOnClickListener {
            val mPopWindowPromotion = ShowPromotionPopWindowNew(mContext)
            mPopWindowPromotion.mCouponEntryType = mCouponEntryType
            mPopWindowPromotion.setRowsBean(rowsBean)
            mPopWindowPromotion.setSkuId("${rowsBean.id}")
            mPopWindowPromotion.show(tag7)
        }
//
//
//        try {
//            val marketTag: ShopNameWithTagView = baseViewHolder.getView(R.id.snwtg_spell_group_market_tag)
//            val tags = getAllTags()
//            marketTag.visibility = if (tags.isNotEmpty()) View.VISIBLE else View.GONE
//            marketTag.bindData(tags, maxTagCount = 100)
//        } catch (e: Exception) {
//            e.printStackTrace()
//        }
    }

    open fun getAllTags(): MutableList<TagBean> {
        // <PRD-8>”未签署控销协议商品“显示价格 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=776143984
        // 签署协议后可买 优先级第一
        // 老逻辑 先取tas.dataTags 再取tas.productOtherTagList
        var tags = mutableListOf<TagBean>()
        rowsBean.tags?.productOtherTagList?.also { tags += it } 
        rowsBean.tags?.dataTags?.also { tags += it }
        if (tags.size > 2) {
            tags = tags.subList(0, 2)
        }
        return tags
    }

    override fun onCoupon() {
        val tag7: ShopNameWithTagView = baseViewHolder.getView(R.id.rl_icon_type)
        val ivPromotionMore: ImageView = baseViewHolder.getView(R.id.iv_promotion_more)
        var tags = mutableListOf<TagBean>()
        rowsBean.tags?.productOtherTagList?.also { tags += it }
        rowsBean.tags?.dataTags?.also { tags += it }

        if ( tags.size != 0 && !isHiddenPromotionMore) {
            ivPromotionMore.visibility = View.VISIBLE
        } else {
            ivPromotionMore.visibility = View.GONE
        }
        ivPromotionMore.setOnClickListener {
            SearchProductReport.trackSearchItemBtnClickMore(
                mContext,
                baseViewHolder.bindingAdapterPosition,
                rowsBean
            )
            val mPopWindowPromotion = ShowPromotionPopWindowNew(mContext)
            mPopWindowPromotion.mCouponEntryType = mCouponEntryType
            mPopWindowPromotion.setRowsBean(rowsBean)
            mPopWindowPromotion.setSkuId("${rowsBean.id}")
            mPopWindowPromotion.show(tag7)
        }
    }

    override fun onShop(isShowShopInfo: Boolean) {
        if (!rowsBean.shopUrl.isNullOrEmpty() && isShowShopInfo) {
            baseViewHolder.setGone(R.id.cl_pop_company, true)
            val tvPopCompany = baseViewHolder.getView<TextView>(R.id.tv_pop_company)
            if (rowsBean.isOPSingleGoods) {
                baseViewHolder.setVisible(
                    R.id.cl_pop_company,
                    !rowsBean.oPSingleGoodsActName.isNullOrEmpty() || !rowsBean.shopUrl.isNullOrEmpty()
                )
                // 运营位单品
                if (rowsBean.oPSingleGoodsActName.isNullOrEmpty()) {
                    baseViewHolder.setText(R.id.tv_pop_company, rowsBean.shopName)
                } else {
                    val actDrawable =
                        ContextCompat.getDrawable(mContext, R.drawable.icon_op_act_entry)
                    actDrawable?.setBounds(
                        0,
                        0,
                        actDrawable.intrinsicWidth,
                        actDrawable.intrinsicHeight
                    )
                    baseViewHolder.setText(R.id.tv_pop_company, rowsBean.oPSingleGoodsActName)
                }
                baseViewHolder.setVisible(R.id.ll_shop_same_goods, false)
            } else if (rowsBean.hasSimilarGoods == 0) {
                baseViewHolder.setText(R.id.tv_pop_company, rowsBean.shopName)
                baseViewHolder.setVisible(R.id.ll_shop_same_goods, false)
            } else {
                baseViewHolder.setText(R.id.tv_pop_company, rowsBean.shopName)
                baseViewHolder.setVisible(R.id.ll_shop_same_goods, true)
                baseViewHolder.getView<LinearLayout>(R.id.ll_shop_same_goods).setOnClickListener {
                    SearchProductReport.trackSearchItemBtnClickShopSame(
                        mContext,
                        baseViewHolder.bindingAdapterPosition,
                        rowsBean,
                        "${SpmUtil.checkReportSpmFormat(rowsBean.shopName)}_shop-${rowsBean.shopCode}"
                    )
                    val action =
                        "ybmpage://SameGoodsForShopActivity?${IntentCanst.JG_ENTRANCE}=${jgTrackBean?.entrance ?: ""}&goodsName=${rowsBean.originalShowName}&shopCodes=${rowsBean.shopCode}&masterStandardProductId=${rowsBean.masterStandardProductId ?: ""}&shopName=${rowsBean.shopName}"
                    RoutersUtils.open(action)
                    XyyIoUtil.track(
                        "action_commodityCard_more", hashMapOf(
                            "shopCode" to rowsBean.shopCode,
                            "commodityName" to rowsBean.showName,
                            "action" to action
                        ), rowsBean
                    )
                }
            }

            val companyClickListener = View.OnClickListener {
                var mUrl = if (rowsBean.isOPSingleGoods) {
                    //单品运营位
                    if (rowsBean.opSingleGoodsActJumpUrl.isNullOrEmpty()) {
                        SearchProductReport.trackSearchItemBtnClickShopEntry(mContext, baseViewHolder.bindingAdapterPosition, rowsBean, "${SpmUtil.checkReportSpmFormat(rowsBean.shopName)}_shop-${rowsBean.shopCode}")
                        rowsBean.shopUrl
                    } else {
                        SearchProductReport.trackSearchItemBtnClickSingleOpShopEntry(mContext, baseViewHolder.bindingAdapterPosition, rowsBean, "${SpmUtil.checkReportSpmFormat(rowsBean.oPSingleGoodsActName)}_h5-${rowsBean.activityPageId?: 0}")
                        rowsBean.opSingleGoodsActJumpUrl
                    }
                } else {
                    SearchProductReport.trackSearchItemBtnClickShopEntry(mContext, baseViewHolder.bindingAdapterPosition, rowsBean, "${SpmUtil.checkReportSpmFormat(rowsBean.shopName)}_shop-${rowsBean.shopCode}")
                    rowsBean.shopUrl
                }
                val mParams = HashMap<String, String>()
                mParams[IntentCanst.JG_REFERRER] = jgTrackBean?.jgReferrer ?: ""
                mParams[IntentCanst.JG_REFERRER_TITLE] = jgTrackBean?.jgReferrerTitle ?: ""
                mParams[IntentCanst.JG_ENTRANCE] = jgTrackBean?.entrance ?: ""
                mUrl = splicingUrlWithParams(
                    mUrl,
                    mParams
                )

                RoutersUtils.open(mUrl)
                if (rowsBean.isOPSingleGoods) {
                    //运营位单品
                    XyyIoUtil.track(
                        "search_Active_Click", hashMapOf(
                            "active_url" to rowsBean.opSingleGoodsActJumpUrl,
                            "active_title" to rowsBean.oPSingleGoodsActName,
                        )
                    )
                }
            }
            baseViewHolder.setOnClickListener(R.id.tv_pop_company, companyClickListener)
            try {
                baseViewHolder.setOnClickListener(R.id.tv_goto_shop, companyClickListener)
                baseViewHolder.setOnClickListener(R.id.iv_goto_shop, companyClickListener)
            } catch (_: Exception) {

            }
        } else {
            baseViewHolder.setGone(R.id.cl_pop_company, false)
        }
    }

    override fun onArrivalOfGoodsNotify(adapter: RecyclerView.Adapter<*>) {
        val ivGoodsSubscribe = baseViewHolder.getView<ImageView>(R.id.iv_goods_subscribe)
        val tvGoodsSubscribe = baseViewHolder.getView<TextView>(R.id.tv_goods_subscribe)
        if ((rowsBean.status == 2 || rowsBean.status == 4 || rowsBean.availableQty <= 0)) {
            if (rowsBean.favoriteStatus == 2) {
                ivGoodsSubscribe.setBackgroundResource(R.drawable.icon_goods_arrival)
                tvGoodsSubscribe.text = "到货通知"
            } else {
                ivGoodsSubscribe.setBackgroundResource(R.drawable.icon_goods_subscribe)
                tvGoodsSubscribe.text = " 已订阅"
            }
        }
        ivGoodsSubscribe.setOnClickListener {
            checkCollect(
                rowsBean,
                baseViewHolder.adapterPosition,
                adapter
            )
        }
    }

    override fun onSellOut() {
        val tvShopNoLimit = baseViewHolder.getView<TextView>(R.id.shop_no_limit_tv01)
        val llSubscribe = baseViewHolder.getView<LinearLayout>(R.id.ll_subscribe)
        val btn = baseViewHolder.getView<TextView>(R.id.tv_join_groupbooking)

        val sellOutStr = mContext.resources.getString(R.string.text_sell_out)
        if ((rowsBean.status == 2 || rowsBean.status == 4 || rowsBean.availableQty <= 0)) {
            tvShopNoLimit.visibility = View.VISIBLE
            llSubscribe.visibility = View.VISIBLE
            if (rowsBean.status == 2 || rowsBean.availableQty <= 0) {
                //售罄
                tvShopNoLimit.text = sellOutStr
            }
            btn.isEnabled = false
        } else {
            //售罄等字样不显示
            tvShopNoLimit.visibility = View.GONE
            llSubscribe.visibility = View.GONE
            btn.isEnabled = true
        }
    }

    override fun onOffShelf() {
        val tvShopNoLimit = baseViewHolder.getView<TextView>(R.id.shop_no_limit_tv01)
        val llSubscribe = baseViewHolder.getView<LinearLayout>(R.id.ll_subscribe)
        val soldOutStr = mContext.resources.getString(R.string.text_sold_out)
        if ((rowsBean.status == 2 || rowsBean.status == 4 || rowsBean.availableQty <= 0)) {
            tvShopNoLimit.visibility = View.VISIBLE
            llSubscribe.visibility = View.VISIBLE
            if (rowsBean.status == 4) {
                tvShopNoLimit.text = soldOutStr
            }
        } else {
            tvShopNoLimit.visibility = View.GONE
            llSubscribe.visibility = View.GONE
        }
    }

    override fun onAddCart(pageFrom: Int, rowsBean: RowsBean, index: String) {
        val editLayout = baseViewHolder.getView<ProductEditLayoutNew>(R.id.el_edit)
        val secKillEditLayout = baseViewHolder.getView<ProductEditLayoutNew>(R.id.el_sec_kill_edit)
        setProductEdit(editLayout, pageFrom, rowsBean, index)
        setProductEdit(secKillEditLayout, pageFrom, rowsBean, index)
        secKillEditLayout.visibility = View.GONE
        secKillEditLayout.setOnDelProductListener { number ->
            //点击也认为是点击商品埋点
            productClickTrackListener?.invoke(
                rowsBean,
                baseViewHolder.bindingAdapterPosition,
                true,
                "加购",
                number
            )
            secKillEditLayout.visibility = View.GONE
        }
    }

    private fun setProductEdit(
        editlayout: ProductEditLayoutNew,
        pageFrom: Int,
        rowsBean: RowsBean,
        index: String
    ) {
        try {
            editlayout.rowsBeanPosition = Integer.parseInt(index)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        editlayout.isAddCartShowPopupWindow = isAddCartShowPopupWindow
        // 店铺搜索页 普通商品也要和大搜页面一样弹窗处理
        if (jgTrackBean?.pageId.equals("dianpuneisousuoye")) {
            editlayout.isAddCartShowPopupWindow = true
        }
        rowsBean.jgTrackBean = jgTrackBean
        editlayout.jgTrackBean = jgTrackBean
        editlayout.jgPageListCommonBean = jgPageListCommonBean
        editlayout.rowsBean = rowsBean
        editlayout.visibility = View.VISIBLE
        editlayout.mSource = rowsBean.sourceType
        editlayout.mIndex = index
        // 处理中包装逻辑
        editlayout.setOnFoldingListener {
            if (onShowMiddlePackage()) baseViewHolder.setGone(R.id.shop_price_tv, !it)
        }
        editlayout.mSId = mFlowData?.sId
        editlayout.mSpId = mFlowData?.spId
        editlayout.mSpType = mFlowData?.spType
        editlayout.setOnNumberListener { view, number ->
            //点击加购也认为是点击商品埋点
            productClickTrackListener?.invoke(
                rowsBean,
                baseViewHolder.bindingAdapterPosition, true, "加购", number
            )
        }
        editlayout.setOnAddCartListener(object : ProductEditLayoutNew.AddCartListener {
            override fun onPreAddCart(params: RequestParams): RequestParams {
                addAnalysisRequestParams(
                    params,
                    mFlowData,
                    AnalysisConst.FlowDataChain.FLOWDATACHAIN_TAG_ADDCART_LIST
                )
                if (rowsBean.nsid != null) {
                    params.put("nsid", rowsBean.nsid)
                    params.put("sdata", rowsBean.sdata ?: "")
                }
                val jgRequestParams = JgRequestParams()
                jgRequestParams.entrance = jgTrackBean?.entrance ?: ""
                jgRequestParams.activity_entrance = jgTrackBean?.activityEntrance ?: ""
                jgRequestParams.search_sort_strategy_id = getSuperProperty(
                    mContext,
                    JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID
                ) as String?
                JGTrackManager.GlobalVariable.mJgOperationInfo?.let {
                    if (!it.productId.isNullOrEmpty() && it.productId == rowsBean.productId) {
                        jgRequestParams.operation_id =
                            jgTrackBean?.mJgOperationPositionInfo?.operationId ?: ""
                        jgRequestParams.operation_rank =
                            jgTrackBean?.mJgOperationPositionInfo?.operationRank ?: 1
                        jgRequestParams.rank = jgTrackBean?.mJgOperationPositionInfo?.rank ?: 1
//                        params.put("operationId", jgTrackBean?.mJgOperationPositionInfo?.operationId?:"")
//                        params.put("operationRank", jgTrackBean?.mJgOperationPositionInfo?.operationRank?.toString()?:"1")
//                        params.put("rank", jgTrackBean?.mJgOperationPositionInfo?.rank?.toString()?:"1")
                    }
                }
                JGTrackManager.GlobalVariable.mJgSearchRowsBean?.let {
                    if (!it.productId.isNullOrEmpty() && it.productId == rowsBean.productId) {
                        jgRequestParams.list_position_type = rowsBean.positionType.toString()
                        jgRequestParams.key_word = rowsBean.searchKeyword
                        jgRequestParams.list_position_typename = rowsBean.positionTypeName ?: ""
                    }
                }

                return params
            }

            override fun onAddCartSuccess(params: ProductEditLayoutSuccessParams) {
                if (params.addType == BUTTON_TYPE_ADD || params.addType == BUTTON_TYPE_INPUT) {
                    ToastUtils.showShort("加入购物车成功")
                }
                reportAddToCart(
                    AddToCart(
                        url = jgTrackBean?.url ?: "",
                        title = jgTrackBean?.title ?: "",
                        referrer = jgTrackBean?.jgReferrer ?: "",
                        jGPageListCommonBean = jgPageListCommonBean,
                        search_sort_strategy_id = rowsBean.searchSortStrategyCode ?: "",
                        rank = jgTrackBean?.mJgOperationPositionInfo?.rank,
                        operation_id = jgTrackBean?.mJgOperationPositionInfo?.operationId ?: "",
                        operation_rank = jgTrackBean?.mJgOperationPositionInfo?.operationRank,
                        list_position_type = rowsBean.positionType.toString(),
                        list_position_typename = rowsBean.positionTypeName ?: "",
                        product_id = rowsBean.id,
                        product_name = rowsBean.productName ?: "",
                        product_first = rowsBean.categoryFirstId,
                        product_price = rowsBean.jgProductPrice,
                        product_type = rowsBean.productType.toString(),
                        direct = "1",
                        product_number = params.amount?.toIntOrNull(),
                        product_activity_type = rowsBean.productActivityType,
                        product_shop_code = rowsBean.shopCode,
                        product_shop_name = rowsBean.shopName,
                    )
                )

                if (mFlowData != null) {
                    flowDataPageCommodityDetails(
                        mFlowData,
                        rowsBean.id.toString() + "",
                        AnalysisConst.FlowDataChain.FLOWDATACHAIN_TAG_INTOPRODUCTDETAIL_NOREAL,
                        rowsBean.sourceType,
                        "${baseViewHolder.bindingAdapterPosition}"
                    )
                    if (rowsBean.nsid != null) {
                        flowDataPageCommodityDetailsForFeed(
                            rowsBean.nsid,
                            rowsBean.sdata
                                ?: "",
                            rowsBean.productId,
                            rowsBean.showName,
                            rowsBean.barcode,
                            AnalysisConst.FlowDataChain.FLOWDATACHAIN_TAG_INTOPRODUCTDETAIL_NOREAL
                        )
                    }
                }
            }
        })

        editlayout?.bindData(
            rowsBean.id,
            rowsBean.status,
            true,
            pageFrom,
            baseViewHolder.getView<View>(R.id.icon) as ImageView,
            true,
            rowsBean.stepNum,
            rowsBean.isSplit == 1
        )
        if (rowsBean.isControlGoods) {
            editlayout.visibility = View.VISIBLE
        } else {
            if (rowsBean.showPriceType() == 0 && !(rowsBean.status == 2 || rowsBean.status == 4 || rowsBean.availableQty <= 0)) {
                editlayout.visibility = View.VISIBLE
            } else {
                editlayout.visibility = View.GONE
            }
        }
    }

    open fun onShowMiddlePackage() = true

    override fun onHandleAuditPassedVisible() {
        handleAuditPassedVisible(baseViewHolder)
    }

    override fun onSpellGroupOrSeckill() {
        // 如果遇到刷新数据，要先取消原来的倒计时
        countDownTimerMap.get(baseViewHolder.hashCode())?.let {
            it.cancel()
            countDownTimerMap.remove(baseViewHolder.hashCode())
        }
        val clSecKillTime = baseViewHolder.getView<ConstraintLayout>(R.id.clSecKillTime)
        clSecKillTime.visibility = View.GONE
        baseViewHolder.getView<TextView>(R.id.seckill_desc)?.visibility = View.GONE
        baseViewHolder.getView<SeekBar>(R.id.seckill_progress)?.visibility = View.GONE
    }

    override fun onSpellGroupPreHot() {
    }

    override fun onNormal() {
    }

    /**
     * 最后调用
     */
    open fun onFinal() {
        // 处理单价显示
        handleUnitPrice()

    }

    /**
     * 处理价格资质认证可见
     */
    private fun handleAuditPassedVisible(baseViewHolder: YBMBaseHolder) {
        // 商品价格
//        val tvShopPrice = baseViewHolder.getView<TextView>(R.id.shop_price)
//        val tvAuditPassedVisible = baseViewHolder.getView<TextView>(R.id.tv_audit_passed_visible)
//
//        //审核通过
//        if (AuditStatusSyncUtil.getInstance().isAuditFirstPassed) {
//            tvShopPrice.visibility = View.VISIBLE
//            tvAuditPassedVisible.visibility = View.GONE
//        } else {
//            tvShopPrice.visibility = View.GONE
//            tvAuditPassedVisible.visibility = View.VISIBLE
//        }
    }

    /**
     * 初始化设置view
     */
    private fun initViewVisibility(baseViewHolder: YBMBaseHolder) {
        // 规格、厂家、分割线、有效期、数据标签、营销标签、营销标签展开按钮、促销价、加购、中包装、有效期（后加）
        baseViewHolder.getView<TextView?>(R.id.tv_goods_spec)?.visibility = View.VISIBLE
        baseViewHolder.getView<ImageView?>(R.id.iv_divider_of_spec_name)?.visibility = View.VISIBLE
        baseViewHolder.getView<TextView?>(R.id.tv_chang_name)?.visibility = View.VISIBLE
//        baseViewHolder.getView<ShopNameWithTagView?>(R.id.data_tag_list_view)?.visibility =
//            View.VISIBLE
        baseViewHolder.getView<ShopNameWithTagView?>(R.id.rl_icon_type)?.visibility = View.VISIBLE
        baseViewHolder.getView<ImageView?>(R.id.iv_promotion_more)?.visibility = View.VISIBLE
        baseViewHolder.getView<TextView?>(R.id.tv_retail_price)?.visibility = View.VISIBLE
        baseViewHolder.getView<ProductEditLayoutNew?>(R.id.el_edit)?.visibility = View.VISIBLE
        baseViewHolder.getView<TextView?>(R.id.shop_price_tv)?.visibility = View.VISIBLE

        baseViewHolder.getView<SeckillTimeView?>(R.id.st_countdown)?.visibility = View.GONE
        baseViewHolder.getView<ProgressBar?>(R.id.seckill_progress)?.visibility = View.GONE
        baseViewHolder.getView<TextView?>(R.id.seckill_desc)?.visibility = View.GONE
        baseViewHolder.getView<TextView?>(R.id.tv_seckill_commit)?.visibility = View.GONE

        // 初始化时拼团相关控件要隐藏
        baseViewHolder.getView<ConstraintLayout?>(R.id.cl_groupbooking)?.visibility = View.GONE
        baseViewHolder.getView<TextView?>(R.id.tv_countdown)?.visibility = View.GONE
    }

    /*
     * 收藏-取消收藏
     * */
    private fun checkCollect(rowsBean: RowsBean, potion: Int, adapter: RecyclerView.Adapter<*>) {
        val id = rowsBean.id
        val collectNet =
            if (rowsBean.favoriteStatus == 1) AppNetConfig.CANCEL_COLLECT else AppNetConfig.COLLECT
        val collectStr = if (rowsBean.favoriteStatus == 1) "取消收藏" else "收藏成功"
        val params = RequestParams()
        val merchantId = SpUtil.getMerchantid()
        params.put("merchantId", merchantId)
        params.put("skuId", id.toString())
        HttpManager.getInstance().post(collectNet, params, object : BaseResponse<EmptyBean?>() {
            override fun onSuccess(content: String?, obj: BaseBean<EmptyBean?>?, t: EmptyBean?) {

                if (obj != null && obj.isSuccess) {
                    if (rowsBean.favoriteStatus == 1) {
                        rowsBean.setFavoriteStatus(2)
                        ToastUtils.showLong(collectStr)
                    } else {
                        rowsBean.setFavoriteStatus(1)
                        showRemindDialog(mContext)
                    }
                    adapter.notifyItemChanged(potion)
                }

            }
        })
    }

    private fun showRemindDialog(context: Context) {
        val str =
            "若该商品在45天内到货，药帮忙会提醒您！ 同时您可以在我的收藏夹查看您订阅过的所有商品"
        val dialogEx = AlertDialogEx(context)
        dialogEx.setMessage(str).setCancelButton("我知道啦",
            AlertDialogEx.OnClickListener { dialog, button -> dialog.dismiss() })
            .setCancelable(false).setCanceledOnTouchOutside(false).setTitle("订阅成功").show()
    }

    fun setItemBackground(resId: Int) {
        val llItemRoot = baseViewHolder.getView<ConstraintLayout>(R.id.ll_item_root)
        llItemRoot.setBackgroundResource(resId)
    }

}