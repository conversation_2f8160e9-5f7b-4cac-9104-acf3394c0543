package com.ybmmarketkotlin.adapter.goodslist.bigpic

import android.content.Context
import android.os.CountDownTimer
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StrikethroughSpan
import android.util.SparseArray
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.analysis.BaseFlowData
import com.ybmmarket20.utils.analysis.openUrl
import com.ybmmarket20.view.ProductEditLayoutNew
import com.ybmmarket20.view.ShopNameWithTagView
import java.text.SimpleDateFormat

/**
 * 预热
 */
open class PreSpellGroupGoodsListAdapterNewBindItemBigPic(
    mContext: Context,
    baseViewHolder: YBMBaseHolder,
    rowsBean: RowsBean,
    countDownTimerMap: SparseArray<CountDownTimer>
) : AbstractGoodsListAdapterNewBindItemBigPic(mContext, baseViewHolder, rowsBean, countDownTimerMap) {

    override fun onGoodsNameWithTags() {
        super.onGoodsNameWithTags()
        baseViewHolder.getView<TextView>(R.id.shop_name).maxLines = 2
    }

    override fun onGoodsPrice(showUnderlinePrice: Boolean, showPgbyUnderLineProce: Boolean) {
        super.onGoodsPrice(showUnderlinePrice, showPgbyUnderLineProce)
        rowsBean.actPt?.let {
            val price = baseViewHolder.getView<TextView>(R.id.shop_price)
            price.text = rowsBean.getShowPriceStrNew(showUnderlinePrice)
        }
    }

    override fun onGoodsSpec() {
        super.onGoodsSpec()
        baseViewHolder.getView<TextView?>(R.id.tv_goods_spec)?.visibility = View.GONE
        baseViewHolder.getView<ImageView?>(R.id.iv_divider_of_spec_name)?.visibility = View.GONE
    }

    override fun onManufacturerTag() {
        super.onManufacturerTag()
        baseViewHolder.getView<TextView?>(R.id.tv_chang_name)?.visibility = View.GONE
    }

    override fun onDataTags() {
        super.onDataTags()
        baseViewHolder.getView<ShopNameWithTagView?>(R.id.data_tag_list_view)?.visibility = View.GONE
    }

    override fun onMarketingTags() {
        super.onMarketingTags()
        baseViewHolder.getView<TextView?>(R.id.tv_retail_price)?.visibility = View.GONE
        val tvGroupbooking = baseViewHolder.getView<TextView?>(R.id.tv_groupbooking)
        tvGroupbooking?.text =
            "已拼${rowsBean.actPt?.orderNum}${rowsBean?.productUnit}/${rowsBean.actPt?.skuStartNum}${rowsBean?.productUnit}起拼"
        tvGroupbooking?.setTextColor(ContextCompat.getColor(mContext, R.color.color_676773))

        // 处理单价显示
        handleUnitPrice()
    }

    override fun onEffectTags() {
        super.onEffectTags()
        baseViewHolder.getView<TextView?>(R.id.tv_validity_period)?.visibility = View.GONE
    }

    override fun onGoodsDiscountPrice() {
        super.onGoodsDiscountPrice()
    }

    override fun onCoupon() {
        super.onCoupon()
        baseViewHolder.getView<ShopNameWithTagView?>(R.id.rl_icon_type)?.visibility = View.GONE
        baseViewHolder.getView<ImageView?>(R.id.iv_promotion_more)?.visibility = View.GONE
    }

    override fun onMediumPackage() {
        super.onMediumPackage()
        baseViewHolder.getView<TextView?>(R.id.shop_price_tv)?.visibility = View.GONE
    }

    override fun onArrivalOfGoodsNotify(adapter: RecyclerView.Adapter<*>) {
        super.onArrivalOfGoodsNotify(adapter)
        baseViewHolder.getView<LinearLayout>(R.id.ll_subscribe)?.visibility = View.GONE
    }

    override fun onSpellGroupPreHot() {
        super.onSpellGroupPreHot()
        val tvCountdownPreHot = baseViewHolder.getView<TextView>(R.id.tv_countdown)
        tvCountdownPreHot?.visibility = View.VISIBLE
        val clSpellGroup = baseViewHolder.getView<ConstraintLayout?>(R.id.cl_groupbooking)
        clSpellGroup?.visibility = View.VISIBLE
        clSpellGroup?.setBackgroundResource(R.drawable.icon_spell_group_btn_pre_bg)
        //时间设置图标
        val drawable = ContextCompat.getDrawable(mContext, R.drawable.icon_countdown_pre_hot)
        drawable?.setBounds(0, 0, drawable.minimumWidth, drawable.minimumHeight)
        tvCountdownPreHot.setCompoundDrawables(drawable, null, null, null)
        //时间设置颜色
        tvCountdownPreHot.setTextColor(ContextCompat.getColor(mContext, R.color.color_00b377))
        //设置开团时间
        val simpleFormat = SimpleDateFormat("MM月dd日 HH:mm")
        val interval = rowsBean.actPt?.assembleStartTime ?: 0 - System.currentTimeMillis()
        if (interval > 0) {
            val time = simpleFormat.format(interval)
            tvCountdownPreHot.text = "${time}开抢"
        }
        //进度条
        val progress = baseViewHolder.getView<ProgressBar>(R.id.progress)
        progress.progressDrawable = ContextCompat.getDrawable(mContext, R.drawable.bg_progressbar_groupbooking_pre_hot)
        progress.progress = 0
        //进度
        val tvProgress = baseViewHolder.getView<TextView>(R.id.tv_groupbooking_progress)
        tvProgress.text = "已拼0%"
        tvProgress.setTextColor(ContextCompat.getColor(mContext, R.color.color_00b377))
        //即将开团
        val btn = baseViewHolder.getView<TextView>(R.id.tv_join_groupbooking)
        btn.text = "即将开团"
        btn.setTextColor(ContextCompat.getColor(mContext, R.color.white))
        btn.setBackgroundResource(R.drawable.icon_spell_group_btn_pre)
        //加购按钮
        baseViewHolder.getView<ProductEditLayoutNew?>(R.id.el_edit)?.visibility = View.GONE
    }

    fun setFlowData(flowData: BaseFlowData?) {
        baseViewHolder.setOnClickListener(R.id.tv_join_groupbooking) {
            openUrl(
                "ybmpage://productdetail?${IntentCanst.PRODUCTID}=${rowsBean.id}&nsid=${rowsBean.nsid ?: ""}&sdata=${rowsBean.sdata ?: ""}",
                flowData
            )

            //点击立即参团也认为是点击商品的埋点
            productClickTrackListener?.invoke(rowsBean,baseViewHolder.bindingAdapterPosition,null)
        }
    }


}