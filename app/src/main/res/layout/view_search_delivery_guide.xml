<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#80000000"
    android:clickable="true"
    android:focusable="true">

    <!-- 手势指引图片 - 将通过代码动态定位 -->
    <ImageView
        android:id="@+id/iv_guide_hands"
        android:layout_width="130dp"
        android:layout_height="134dp"
        android:src="@drawable/mask_hands"
        android:scaleType="centerInside"
        android:clickable="false"
        android:focusable="false" />

    <!-- "我知道了"按钮图片 - 将通过代码动态定位到手势图片下方 -->
    <ImageView
        android:id="@+id/iv_guide_iknow"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:src="@drawable/mask_iknow"
        android:scaleType="centerInside"
        android:adjustViewBounds="true"
        android:clickable="false"
        android:focusable="false" />

</FrameLayout>
