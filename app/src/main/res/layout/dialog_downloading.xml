<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="300dp"
        android:layout_height="140dp"
        android:background="@drawable/bg_dialog_net_warn">

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_gravity="center_horizontal"
            android:background="@drawable/bg_dialog_downloading_title"
            android:gravity="center"
            android:text="正在下载最新版本，请耐心等待"
            android:textColor="#FDFDFD"
            android:textSize="14sp" />


        <ProgressBar
            android:id="@+id/progress"
            style="@style/HorizontalProgressBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_content"
            android:layout_marginLeft="35dp"
            android:layout_marginTop="25dp"
            android:layout_marginRight="35dp"
            tools:progress="30" />

        <TextView
            android:id="@+id/tv_progress_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/progress"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:textColor="#00B377"
            android:textSize="12sp"
            tools:text="已经下载42%" />
    </RelativeLayout>


</LinearLayout>