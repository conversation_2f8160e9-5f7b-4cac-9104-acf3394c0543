<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_marginTop="@dimen/dimen_dp_4"
    android:background="@color/white"
    android:paddingTop="14dp"
    android:paddingBottom="@dimen/dimen_dp_9">


    <CheckBox
        android:id="@+id/cb_group"
        android:layout_width="@dimen/dimen_dp_17"
        android:layout_height="@dimen/dimen_dp_17"
        android:layout_marginLeft="@dimen/dimen_dp_10"
        android:background="@drawable/selector_cart_bottom_coupon_check"
        android:button="@null"
        android:checked="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/tv_real_group_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dimen_dp_10"
        tools:text="搭配套餐：￥550.00"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_13"
        app:layout_constraintBaseline_toBaselineOf="@id/cb_group"
        app:layout_constraintLeft_toRightOf="@id/cb_group" />


    <TextView
        android:id="@+id/tv_real_origin_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dimen_dp_14"
        tools:text="原价：¥850.00"
        android:textColor="@color/color_9494A6"
        android:textSize="@dimen/dimen_dp_12"
        app:layout_constraintBaseline_toBaselineOf="@id/cb_group"
        app:layout_constraintLeft_toRightOf="@id/tv_real_group_price" />

</androidx.constraintlayout.widget.ConstraintLayout>