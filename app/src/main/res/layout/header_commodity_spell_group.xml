<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_home_spell_group_bg">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@id/cl_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:paddingTop="@dimen/dimen_dp_13"
        android:paddingBottom="@dimen/dimen_dp_13"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_15"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="大家都在团" />

        <ImageView
            android:id="@+id/iv_title_endfix"
            android:layout_width="34dp"
            android:layout_height="11dp"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:scaleType="fitXY"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/tv_title"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_more_entry"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dimen_dp_15"
            android:drawableEnd="@drawable/icon_home_arrow_entry"
            android:textColor="@color/color_676773"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="即将开抢" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>