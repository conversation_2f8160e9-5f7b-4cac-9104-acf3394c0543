<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">

    <View
        android:id="@+id/view_bg"
        android:layout_width="60dp"
        android:layout_height="match_parent" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <include layout="@layout/base_pop_filtrate_class_common_title" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="65dp"
            android:background="@color/white"
            android:gravity="center_vertical"
            android:paddingLeft="10dp"
            android:paddingRight="10dp">

            <EditText
                android:id="@+id/et_search"
                android:layout_width="match_parent"
                android:layout_height="38dp"
                android:layout_centerVertical="true"
                android:background="@drawable/search_round_corner_gray_bg_03"
                android:drawableLeft="@drawable/manufacturers_search"
                android:drawablePadding="6dp"
                android:hint="搜索厂商名字"
                android:imeOptions="actionSearch"
                android:maxLines="1"
                android:paddingLeft="8dp"
                android:paddingRight="8dp"
                android:singleLine="true"
                android:textColor="@color/text_292933"
                android:textColorHint="@color/text_9494A6"
                android:textSize="14sp" />

            <ImageView
                android:id="@+id/iv_del"
                android:layout_width="35dp"
                android:layout_height="35dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="8dp"
                android:padding="6dp"
                android:src="@drawable/clear_sousou"
                android:visibility="gone" />
        </RelativeLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/white">

            <com.ybm.app.view.CommonRecyclerView
                android:id="@+id/rv_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <!--   修改排序逻辑这里 隐藏-->
            <com.ybmmarket20.view.IndexBar
                android:id="@+id/indexBar"
                android:visibility="gone"
                android:layout_width="24dp"
                android:layout_height="wrap_content"
                android:layout_gravity="right|center_vertical"
                android:paddingTop="6dp"
                android:paddingBottom="6dp"
                app:indexBarPressBackground="@color/partTranslucent"
                app:indexBarTextSize="11sp" />

            <TextView
                android:id="@+id/tvSideBarHint"
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:layout_gravity="center"
                android:background="@drawable/shape_side_bar_bg"
                android:gravity="center"
                android:textColor="@color/white"
                android:textSize="44sp"
                android:visibility="gone"
                tools:text="A"
                tools:visibility="visible" />

        </FrameLayout>
    </LinearLayout>
</LinearLayout>
