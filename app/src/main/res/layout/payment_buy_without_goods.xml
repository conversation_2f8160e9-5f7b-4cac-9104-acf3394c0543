<?xml version="1.0" encoding="utf-8"?>
<com.ybmmarket20.common.widget.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:rv_backgroundColor="@color/white"
    app:rv_cornerRadius="2dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="随心拼"
        android:textStyle="bold"
        android:layout_marginTop="@dimen/dimen_dp_13"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_15"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_buy_more"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/dimen_dp_14"
        android:text="购买更多"
        android:textColor="@color/color_00b377"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:drawableEnd="@drawable/icon_suixinpin_right_arrow"
        app:layout_constraintBottom_toBottomOf="@+id/tv_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_title" />

    <View
        android:id="@+id/divider_top"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0_5"
        android:layout_marginTop="@dimen/dimen_dp_13"
        android:background="@color/color_F5F5F5"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/divider_top"
        android:layout_marginTop="@dimen/dimen_dp_10"
        tools:itemCount="3"
        tools:listitem="@layout/item_spell_group_recommend_goods" />

    <View
        android:id="@+id/divider_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0_5"
        android:background="@color/color_F5F5F5"
        app:layout_constraintTop_toBottomOf="@+id/rv" />

    <TextView
        android:id="@+id/tv_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="0种共0件"
        android:textColor="@color/color_9494A6"
        android:layout_marginTop="@dimen/dimen_dp_14"
        android:layout_marginBottom="@dimen/dimen_dp_14"
        android:layout_marginStart="@dimen/dimen_dp_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/divider_bottom" />

    <TextView
        android:id="@+id/tv_total_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/dimen_dp_16"
        android:textColor="@color/color_ff2121"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:text="小计: ¥0.00"
        app:layout_constraintBottom_toBottomOf="@+id/tv_count"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_count" />

</com.ybmmarket20.common.widget.RoundConstraintLayout>