<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="@dimen/dimen_dp_10"
    android:paddingEnd="@dimen/dimen_dp_10"
    android:paddingBottom="@dimen/dimen_dp_10"
    android:orientation="vertical"
    android:background="@color/white">

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="需要补发的资质"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_15"
        android:layout_marginTop="@dimen/dimen_dp_15"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</LinearLayout>