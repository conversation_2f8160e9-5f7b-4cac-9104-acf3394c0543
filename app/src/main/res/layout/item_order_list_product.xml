<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginEnd="8dp"
    xmlns:tools="http://schemas.android.com/tools">

    <com.ybmmarket20.common.widget.RoundedImageView
        android:id="@+id/iv_order"
        android:layout_width="78dp"
        android:layout_height="78dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="2dp" />

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/tv_goods_num"
        android:layout_width="wrap_content"
        android:layout_height="16dp"
        app:rv_backgroundColor="@color/color_80000000"
        android:paddingHorizontal="5dp"
        android:minWidth="25dp"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="12dp"
        app:rv_cornerRadius="2dp"
        app:layout_constraintBottom_toBottomOf="@id/iv_order"
        app:layout_constraintEnd_toEndOf="@id/iv_order"
        tools:text="x2" />

</androidx.constraintlayout.widget.ConstraintLayout>