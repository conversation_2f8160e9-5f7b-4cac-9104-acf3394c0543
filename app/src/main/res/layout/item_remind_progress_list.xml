<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#FFFFFF"
    android:paddingStart="@dimen/dimen_dp_16"
    android:paddingEnd="@dimen/dimen_dp_16">

    <View
        android:id="@+id/view0"
        android:layout_width="0.5dp"
        android:layout_height="0dp"
        android:background="@drawable/rotate_rectangle_dotted_stroke"
        app:layout_constraintBottom_toTopOf="@id/iv_img"
        app:layout_constraintEnd_toEndOf="@id/iv_img"
        app:layout_constraintStart_toStartOf="@id/iv_img"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_img"
        android:layout_width="13dp"
        android:layout_height="13dp"
        android:scaleType="center"
        android:src="@drawable/selector_remind_progress"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_title" />

    <View
        android:id="@+id/view1"
        android:layout_width="0.5dp"
        android:layout_height="0dp"
        android:background="@drawable/rotate_rectangle_dotted_stroke"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/iv_img"
        app:layout_constraintStart_toStartOf="@id/iv_img"
        app:layout_constraintTop_toBottomOf="@id/iv_img" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginTop="@dimen/dimen_dp_20"
        android:textColor="@color/selector_text_color_00b955"
        android:textSize="15sp"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@id/iv_img"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="平台审核通过" />

    <TextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginTop="@dimen/dimen_dp_20"
        android:textColor="#333333"
        android:textSize="15sp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="2024-07-16 09:38:42" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="11dp"
        android:background="@drawable/shape_f7f7f8_7dp"
        android:padding="13dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_title"
        app:layout_constraintTop_toBottomOf="@id/tv_title">

        <TextView
            android:id="@+id/tv_type_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="申诉类型："
            android:textColor="#777777"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#333333"
            android:textSize="14sp"
            app:layout_constraintStart_toEndOf="@id/tv_type_title"
            app:layout_constraintTop_toTopOf="@id/tv_type_title"
            tools:text="物流停发" />

        <TextView
            android:id="@+id/tv_tips_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="11dp"
            android:text="申诉说明："
            android:textColor="#777777"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_type" />

        <TextView
            android:id="@+id/tv_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#333333"
            android:textSize="14sp"
            app:layout_constraintStart_toEndOf="@id/tv_tips_title"
            app:layout_constraintTop_toTopOf="@id/tv_tips_title"
            tools:text="物流停发" />

        <TextView
            android:id="@+id/tv_img_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="11dp"
            android:text="申诉凭证："
            android:textColor="#777777"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_tips" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_img"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_img_title"
            app:layout_constraintTop_toTopOf="@id/tv_img_title"
            tools:text="物流停发" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>