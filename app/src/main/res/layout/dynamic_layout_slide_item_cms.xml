<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:gravity="center"
        android:text="动态布局标题"
        android:textSize="16sp" />

    <LinearLayout
        android:id="@+id/ll_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="102dp"
        android:orientation="vertical"
        android:paddingLeft="10dp"
        android:paddingTop="10dp">

        <TextView
            android:id="@+id/tv_rec_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text=""
            android:textSize="16sp"
            tools:text="动态布局标题" />

        <TextView
            android:id="@+id/tv_rec_sub_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text=""
            android:textSize="16sp"
            tools:text="动态布局副标题" />

    </LinearLayout>
    <!--增加自己的固定布局-->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_recommend_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />


        <com.ybmmarket20.view.indicator.CircleRecyclerPageIndicator xmlns:app="http://schemas.android.com/apk/res-auto"
            android:id="@+id/crpiCenter"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingStart="5dp"
            android:paddingTop="5dp"
            android:paddingEnd="5dp"
            android:paddingBottom="5dp"
            app:fillColor="@color/home_cms_dot_active"
            app:pageColor="@color/home_cms_dot_defalt"
            app:radius="4dp"
            app:strokeWidth="0dip" />

    </LinearLayout>

</LinearLayout>