<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="10dp"
    android:layout_marginRight="10dp"
    android:background="@color/white"
    android:gravity="center_vertical"
    android:orientation="vertical"
    android:paddingBottom="4dp"
    android:paddingLeft="10dp"
    android:paddingRight="10dp"
    android:paddingTop="6dp">

    <LinearLayout
        android:id="@+id/ll_root"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <CheckBox
            android:id="@+id/cb_choice"
            style="@style/CustomCheckboxTheme"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_gravity="center"
            android:layout_marginLeft="5dp"
            android:background="@android:color/transparent"
            android:clickable="false" />

        <!--置灰按钮-->
        <ImageView
            android:id="@+id/iv_cant_choice"
            android:layout_width="30dp"
            android:layout_gravity="center"
            android:layout_marginLeft="5dp"
            android:paddingEnd="10dp"
            android:visibility="gone"
            android:src="@drawable/product_refund_cant_choice"
            android:layout_height="20dp"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="@dimen/dimen_dp_100"
            android:layout_height="@dimen/dimen_dp_100">

            <ImageView
                android:id="@+id/iv_order"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerVertical="true"
                android:padding="@dimen/product_image_padding" />

            <View
                android:id="@+id/v_near_effective_mask"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone"
                android:background="@drawable/shape_near_effective_icon_mask"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_near_effective_tag"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dimen_dp_22"
                android:text="不支持退货"
                android:textColor="@color/white"
                android:gravity="center"
                android:textSize="@dimen/dimen_dp_13"
                android:visibility="gone"
                android:background="@drawable/shape_near_effective_icon_unrefund"
                app:layout_constraintBottom_toBottomOf="parent"
                tools:visibility="visible" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="8dp"
            android:orientation="vertical"
            android:paddingTop="1dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:gravity="center_vertical">

                <ImageView
                    android:id="@+id/tv_procurement_festival"
                    android:layout_width="42dp"
                    android:layout_height="17dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="5dp"
                    android:background="@drawable/icon_procurement_festival"
                    android:paddingLeft="5dp"
                    android:paddingRight="5dp"
                    android:textColor="@color/white"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/tv_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:text=""
                    android:textColor="@color/text_292933"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center_vertical"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_spec"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:singleLine="true"
                    android:text="规格"
                    android:textColor="@color/text_9494A6"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tv_package_num"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:includeFontPadding="false"
                    android:singleLine="true"
                    android:text="中包装数量："
                    android:textColor="@color/text_9494A6"
                    android:textSize="12sp" />

            </LinearLayout>

            <RelativeLayout
                android:id="@+id/ll_detail"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_can_bacck"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:includeFontPadding="false"
                    android:singleLine="true"
                    android:text="可退数量："
                    android:textColor="@color/text_9494A6"
                    android:textSize="12sp" />

                <RelativeLayout
                    android:id="@+id/rl_product_edit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginBottom="5dp">

                    <include layout="@layout/product_edit_layout_02" />

                </RelativeLayout>

                <TextView
                    android:id="@+id/tv_near_effective_goods_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="x4"
                    android:visibility="gone"
                    android:layout_alignParentEnd="true"
                    android:layout_alignParentBottom="true"
                    android:layout_marginEnd="@dimen/dimen_dp_20"
                    android:layout_marginBottom="@dimen/dimen_dp_15"
                    android:textColor="@color/color_292933"
                    android:textSize="@dimen/dimen_dp_12"
                    tools:visibility="visible" />

            </RelativeLayout>

        </LinearLayout>
    </LinearLayout>
    <com.ybmmarket20.view.ShopNameWithTagView
        android:id="@+id/snwtvTag"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_marginStart="@dimen/dimen_dp_140"/>
</LinearLayout>
