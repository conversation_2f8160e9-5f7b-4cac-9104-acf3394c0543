<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_F7F7F7">

    <include
        android:id="@+id/title"
        layout="@layout/common_header_items" />

    <TextView
        android:id="@+id/tvTipOrderList"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:drawableLeft="@drawable/icon_orderlist_tips"
        android:drawablePadding="@dimen/dimen_dp_5"
        android:gravity="center_vertical"
        android:paddingLeft="@dimen/dimen_dp_15"
        android:text="@string/orderlist_tips"
        android:textColor="@color/color_9494A6"
        android:textSize="@dimen/dimen_dp_14"
        app:layout_constraintTop_toBottomOf="@+id/title"
        android:visibility="gone"
        tools:visibility="visible" />

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintVertical_weight="1"
        android:layout_marginTop="@dimen/dimen_dp_10"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="@dimen/dimen_dp_2"
        app:cardElevation="@dimen/dimen_dp_0"
        app:layout_constraintBottom_toTopOf="@+id/clConfirmLayout"
        app:layout_constraintTop_toBottomOf="@+id/tvTipOrderList">

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:id="@+id/cl"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/abl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:layout_marginStart="@dimen/dimen_dp_10"
                android:layout_marginEnd="@dimen/dimen_dp_10"
                app:elevation="@dimen/dimen_dp_0">

                <TextView
                    android:id="@+id/tvShopName"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:drawableStart="@drawable/icon_shop_title"
                    android:drawablePadding="@dimen/dimen_dp_4"
                    android:layout_marginTop="@dimen/dimen_dp_10"
                    tools:text="江西明鑫健康产业集团有限公司"
                    android:textStyle="bold"
                    android:textColor="@color/color_292933"
                    android:textSize="@dimen/dimen_dp_14" />

                <TextView
                    android:id="@+id/tvCompanyLicenseTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:drawablePadding="@dimen/dimen_dp_4"
                    android:layout_marginTop="@dimen/dimen_dp_23"
                    android:text="企业相关资质"
                    android:textStyle="bold"
                    android:textColor="@color/color_292933"
                    android:textSize="@dimen/dimen_dp_14" />

                <com.google.android.flexbox.FlexboxLayout
                    android:id="@+id/flLicense"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dimen_dp_12"
                    app:flexWrap="wrap"
                    tools:layout_height="@dimen/dimen_dp_50" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clSearch"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dimen_dp_30"
                    android:layout_marginTop="@dimen/dimen_dp_33">

                    <TextView
                        android:id="@+id/tvGoodsTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawablePadding="@dimen/dimen_dp_4"
                        android:text="商品相关资质"
                        android:textStyle="bold"
                        android:textColor="@color/color_292933"
                        android:textSize="@dimen/dimen_dp_14"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.ybmmarket20.common.widget.RoundFrameLayout
                        android:id="@+id/rflBg"
                        android:layout_width="@dimen/dimen_dp_0"
                        android:layout_height="@dimen/dimen_dp_30"
                        android:layout_marginStart="@dimen/dimen_dp_10"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/tvGoodsTitle"
                        app:layout_constraintTop_toTopOf="parent"
                        app:rv_backgroundColor="@color/color_F5F5F5"
                        app:rv_cornerRadius="2dp" />

                    <ImageView
                        android:id="@+id/ivSearchIcon"
                        android:layout_width="@dimen/dimen_dp_18"
                        android:layout_height="@dimen/dimen_dp_18"
                        android:layout_marginStart="@dimen/dimen_dp_5"
                        android:src="@drawable/icon_search_license_goods"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="@+id/rflBg"
                        app:layout_constraintTop_toTopOf="parent" />

                    <EditText
                        android:id="@+id/etLicense"
                        android:layout_width="@dimen/dimen_dp_0"
                        android:layout_height="wrap_content"
                        android:hint="输入商品名称搜索"
                        android:textSize="@dimen/dimen_dp_13"
                        android:background="@color/transparent"
                        android:layout_marginStart="@dimen/dimen_dp_5"
                        android:layout_marginEnd="@dimen/dimen_dp_35"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/ivSearchIcon"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:id="@+id/ivEditClose"
                        android:layout_width="@dimen/dimen_dp_22"
                        android:layout_height="@dimen/dimen_dp_22"
                        android:src="@drawable/icon_license_close"
                        android:visibility="gone"
                        tools:visibility="visible"
                        android:layout_marginEnd="@dimen/dimen_dp_10"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>


            </com.google.android.material.appbar.AppBarLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvLicense"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_behavior="@string/appbar_scrolling_view_behavior" />
        </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </androidx.cardview.widget.CardView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clConfirmLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_64"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/rtvConfirm"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_44"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginEnd="@dimen/dimen_dp_10"
            android:text="确定"
            android:textColor="@color/white"
            android:gravity="center"
            android:textSize="@dimen/dimen_dp_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:rv_backgroundColor="@color/color_theme_base_color" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>