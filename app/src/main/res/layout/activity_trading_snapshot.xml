<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/activity_bg"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/sl_trading_snapshot"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp_10"
                android:background="@drawable/bg_4corner_ffffff"
                android:padding="@dimen/dp_10"
                android:text="@string/trading_snapshot_list_title_tips"
                android:textColor="@color/text_292933"
                android:textSize="@dimen/sp_14" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/conTips"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:background="@drawable/bg_4corner_ffffff"
                android:layout_marginEnd="10dp"
                android:visibility="gone"
                android:layout_marginBottom="10dp"
                android:padding="10dp">

                <TextView
                    android:id="@+id/tvTipsTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:text="关于电汇商业渠道支付声明"
                    android:textColor="@color/text_color_333333"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvTipsContent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:textColor="@color/text_color_333333"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvTipsTitle"
                    tools:text="您在2025年4月8日13:46:08 已同意平台免责声明" />

                <TextView
                    android:id="@+id/tvMore"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:drawablePadding="5dp"
                    android:text="查看"
                    android:textColor="@color/color_00B955"
                    android:textSize="14sp"
                    app:drawableEndCompat="@drawable/icon_trading_snapshot_arrow"
                    app:layout_constraintBottom_toBottomOf="@id/tvTipsTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvTipsTitle" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/dp_10">

                <TextView
                    android:id="@+id/goodsNumTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_292933"
                    android:textSize="15sp" />


                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerview_goods"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/dimen_dp_14"
                    android:background="@color/white" />

            </LinearLayout>


        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>