<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/hintTv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#FFF7EF"
                android:paddingLeft="20dp"
                android:paddingTop="10dp"
                android:paddingRight="20dp"
                android:paddingBottom="10dp"
                android:text="密码必须8-16个字符，至少含数字、字母、符号两种组合，首位必须为字母"
                android:textColor="#99664d"
                android:textSize="@dimen/dimen_dp_14" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingLeft="20dp"
                android:paddingRight="20dp">

                <!--输入名称-->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/registerPhoneInputName"
                    android:layout_width="0dp"
                    android:layout_height="43dp"
                    android:layout_marginTop="9dp"
                    android:textColorHint="@color/loginTextAppearance"
                    app:hintTextAppearance="@style/inputLayoutHintAppearance"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/register_name"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@null"
                        android:gravity="center_vertical"
                        android:hint="@string/please_enter_name"
                        android:maxLength="8"
                        android:singleLine="true"
                        android:textColor="#292933"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <View
                    android:id="@+id/divider1"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="9dp"
                    android:background="#F5F5F5"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/registerPhoneInputName" />

                <!--输入手机号码-->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/registerPhoneInputLayout"
                    android:layout_width="0dp"
                    android:layout_height="43dp"
                    android:layout_marginTop="9dp"
                    android:textColorHint="@color/loginTextAppearance"
                    app:hintTextAppearance="@style/inputLayoutHintAppearance"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/divider1">

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/register_phone"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@null"
                        android:gravity="center_vertical"
                        android:hint="@string/please_enter_phone"
                        android:inputType="phone"
                        android:maxLength="13"
                        android:singleLine="true"
                        android:textColor="#292933"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <View
                    android:id="@+id/divider2"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="9dp"
                    android:background="#F5F5F5"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/registerPhoneInputLayout" />

                <!--输入图形验证码-->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/registerImgCodeInputLayout"
                    android:layout_width="0dp"
                    android:layout_height="43dp"
                    android:layout_marginTop="9dp"
                    android:textColorHint="@color/loginTextAppearance"
                    app:hintTextAppearance="@style/inputLayoutHintAppearance"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="@id/register_request_img_code"
                    app:layout_constraintTop_toBottomOf="@id/divider2">

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/register_img_code"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@null"
                        android:gravity="center_vertical"
                        android:hint="@string/please_enter_img_code"
                        android:maxLength="8"
                        android:singleLine="true"
                        android:textColor="#292933"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/register_request_img_code"
                    android:layout_width="80dp"
                    android:layout_height="31dp"
                    android:layout_marginBottom="9dp"
                    app:layout_constraintBottom_toTopOf="@id/divider3"
                    app:layout_constraintRight_toRightOf="parent" />

                <View
                    android:id="@+id/divider3"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="9dp"
                    android:background="#F5F5F5"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/registerImgCodeInputLayout" />

                <!--输入短信验证码-->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/registerSmsCodeInputLayout"
                    android:layout_width="0dp"
                    android:layout_height="43dp"
                    android:layout_marginTop="9dp"
                    android:textColorHint="@color/loginTextAppearance"
                    app:hintTextAppearance="@style/inputLayoutHintAppearance"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="@id/register_request_sms_code"
                    app:layout_constraintTop_toBottomOf="@id/divider3">

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/register_sms_code"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@null"
                        android:gravity="center_vertical"
                        android:hint="@string/please_enter_sms_code"
                        android:maxLength="8"
                        android:singleLine="true"
                        android:textColor="#292933"
                        android:textColorHint="@color/loginTextAppearance"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/register_request_sms_code"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="获取短信验证码"
                    android:textColor="#00b377"
                    android:textSize="15sp"
                    app:layout_constraintBottom_toBottomOf="@id/registerSmsCodeInputLayout"
                    app:layout_constraintBottom_toTopOf="@id/divider4"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@id/registerSmsCodeInputLayout" />

                <View
                    android:id="@+id/divider4"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="9dp"
                    android:background="#F5F5F5"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/registerSmsCodeInputLayout" />

                <!--输入密码-->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_reg_pwd"
                    android:layout_width="0dp"
                    android:layout_height="43dp"
                    android:layout_marginTop="9dp"
                    android:textColorHint="@color/loginTextAppearance"
                    app:hintTextAppearance="@style/inputLayoutHintAppearance"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider4"
                    app:passwordToggleDrawable="@drawable/selector_password_toggle"
                    app:passwordToggleEnabled="true">

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/register_password"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@null"
                        android:gravity="center_vertical"
                        android:hint="@string/please_enter_password"
                        android:inputType="textPassword"
                        android:textColor="#292933"
                        android:textSize="15sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <View
                    android:id="@+id/divider5"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="9dp"
                    android:background="#F5F5F5"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/til_reg_pwd" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>
    </ScrollView>

    <com.ybmmarket20.view.CheckPrivacyView
        android:id="@+id/checkPrivacy"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_20"
        android:layout_marginEnd="@dimen/dimen_dp_20"
        android:layout_marginTop="@dimen/dimen_dp_9" />

    <com.ybmmarket20.view.ButtonObserver
        android:id="@+id/register_btn"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_44"
        android:layout_marginTop="@dimen/dimen_dp_11"
        android:layout_marginBottom="@dimen/dimen_dp_15"
        android:background="@drawable/selector_common_btn"
        android:enabled="false"
        android:gravity="center"
        android:text="@string/next_step"
        android:textColor="@color/white"
        android:layout_marginStart="@dimen/dimen_dp_20"
        android:layout_marginEnd="@dimen/dimen_dp_20"
        android:textSize="@dimen/dimen_dp_16" />

</LinearLayout>