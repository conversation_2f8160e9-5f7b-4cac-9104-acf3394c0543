<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="67dp"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:id="@+id/iv_search_start_recommend_goods"
        android:layout_width="@dimen/dimen_dp_57"
        android:layout_height="@dimen/dimen_dp_57"
        android:layout_marginTop="@dimen/dimen_dp_5"
        android:layout_marginStart="@dimen/dimen_dp_10"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/view_search_start_recommend_goods_bg"
        android:layout_width="@dimen/dimen_dp_57"
        android:layout_height="@dimen/dimen_dp_57"
        android:background="@drawable/shape_search_start_recommend_goods_bg"
        app:layout_constraintStart_toStartOf="@+id/iv_search_start_recommend_goods"
        app:layout_constraintTop_toTopOf="@+id/iv_search_start_recommend_goods" />

    <TextView
        android:id="@+id/tv_search_start_recommend_goods_tag"
        android:layout_width="@dimen/dimen_dp_14"
        android:layout_height="@dimen/dimen_dp_17"
        android:background="@drawable/icon_search_start_goods_first"
        android:textColor="@color/white"
        android:textSize="@dimen/dimen_dp_13"
        android:text="1"
        android:gravity="center"
        app:layout_constraintStart_toStartOf="@+id/iv_search_start_recommend_goods"
        app:layout_constraintTop_toTopOf="@+id/iv_search_start_recommend_goods" />

    <TextView
        android:id="@+id/tv_search_start_recommend_goods_spellgroup_price"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_11"
        android:text="拼团价¥12.00"
        android:textSize="@dimen/dimen_dp_7"
        android:gravity="center"
        android:textColor="@color/white"
        android:singleLine="true"
        android:background="@drawable/shape_search_start_recommend_goods_spellgroup"
        app:layout_constraintBottom_toBottomOf="@+id/iv_search_start_recommend_goods"
        app:layout_constraintEnd_toEndOf="@+id/iv_search_start_recommend_goods"
        app:layout_constraintStart_toStartOf="@+id/iv_search_start_recommend_goods" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_search_start_recommend_goods_spellgroup"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_0"
        app:constraint_referenced_ids="view_search_start_recommend_goods_bg, tv_search_start_recommend_goods_spellgroup_price" />

    <TextView
        android:id="@+id/tv_search_start_recommend_goods_title"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_5"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        tools:text="京都 念慈庵 蜜炼川贝枇 杷膏京都 念慈庵 蜜炼川贝枇 杷膏京都 念慈庵 蜜炼川贝枇 杷膏京都 念慈庵 蜜炼川贝枇 杷膏京都 念慈庵 蜜炼川贝枇 杷膏"
        android:textSize="@dimen/dimen_dp_12"
        android:textColor="@color/color_292933"
        android:maxLines="2"
        android:ellipsize="end"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/iv_search_start_recommend_goods"
        app:layout_constraintTop_toTopOf="@+id/iv_search_start_recommend_goods" />

    <TextView
        android:id="@+id/tv_search_start_recommend_goods_spec"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_5"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:textSize="@dimen/dimen_dp_10"
        tools:text="500ml*盒"
        android:singleLine="true"
        android:textColor="@color/color_292933"
        app:layout_constraintBottom_toTopOf="@+id/tv_search_start_recommend_goods_spellgroup_count"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/iv_search_start_recommend_goods" />

    <TextView
        android:id="@+id/tv_search_start_recommend_goods_spellgroup_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_5"
        android:textSize="@dimen/dimen_dp_10"
        tools:text="9999人已拼团"
        android:textColor="#FA2C19"
        app:layout_constraintBottom_toBottomOf="@+id/iv_search_start_recommend_goods"
        app:layout_constraintStart_toEndOf="@+id/iv_search_start_recommend_goods" />


</androidx.constraintlayout.widget.ConstraintLayout>