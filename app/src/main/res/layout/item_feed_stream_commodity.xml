<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:background="@drawable/shape_white_10"
        android:tag="HomeFeedStreamAdapter"
        android:paddingBottom="7dp"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/iv_commodity"
            android:layout_width="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginHorizontal="18dp"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_height="0dp"/>

        <ImageView
            android:id="@+id/iv_marker"
            android:layout_width="100dp"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="@id/iv_commodity"
            app:layout_constraintTop_toTopOf="@id/iv_commodity"
            android:layout_marginTop="1dp"
            android:layout_height="100dp"/>

        <TextView
            android:id="@+id/tv_shop_no_limit"
            android:layout_width="50dp"
            android:layout_height="50dp"
            app:layout_constraintStart_toStartOf="@id/iv_commodity"
            app:layout_constraintEnd_toEndOf="@id/iv_commodity"
            app:layout_constraintTop_toTopOf="@id/iv_commodity"
            app:layout_constraintBottom_toBottomOf="@id/iv_commodity"
            android:background="@drawable/shop_limit01"
            android:gravity="center"
            android:textColor="#ffffff"
            android:textSize="12dp"
            android:visibility="gone" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/group_flash_sale"
            app:constraint_referenced_ids="cl_flash_sale_countdown,cl_flash_sale_price"
            android:layout_width="wrap_content"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_height="wrap_content"/>

        <!--秒杀的倒计时-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_flash_sale_countdown"
            android:layout_width="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:background="@color/color_ea0000"
            app:layout_constraintTop_toBottomOf="@id/iv_commodity"
            android:layout_height="18dp">

            <ImageView
                android:id="@+id/iv_home_limit_skill"
                android:layout_width="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:src="@drawable/icon_home_limit_skill"
                android:layout_marginStart="7dp"
                android:layout_height="wrap_content"/>

            <TextView
                android:id="@+id/tv_time"
                app:layout_constraintStart_toEndOf="@id/iv_home_limit_skill"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_marginStart="6dp"
                android:layout_width="wrap_content"
                android:textSize="10dp"
                android:textColor="@color/white"
                tools:text="距结束03:12:56"
                android:layout_height="wrap_content"/>


        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier1"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="iv_commodity,cl_flash_sale_countdown"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>


        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/barrier1"
            android:maxLines="2"
            android:ellipsize="end"
            android:textSize="14dp"
            android:textColor="@color/color_292933"
            android:textStyle="bold"
            android:layout_marginHorizontal="5dp"
            android:layout_marginTop="1dp"
            tools:text="赛思达速度快发货康康卡数据恢复开始打卡卡收到回复卡可视对讲艾克赛德家复活卡货到付款卡萨帝和卡哈萨克交罚款收到HK安徽省扩大山东科技哈卡奥施康定积分卡号咖啡大师卡数据电话卡夫亨氏看到卡是"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tv_company"
            android:layout_width="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            android:layout_marginTop="4dp"
            android:textColor="@color/color_676773"
            android:textSize="11dp"
            tools:text="吉林省吴太感康药业有限公司"
            android:layout_marginHorizontal="5dp"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tv_expiration_date"
            android:layout_width="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_company"
            android:textColor="@color/color_676773"
            android:textSize="11dp"
            tools:text="有效期：2025.09.29"
            android:layout_marginHorizontal="5dp"
            android:layout_height="wrap_content"/>

        <!--普通商品 价格-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_normal_price"
            android:layout_width="0dp"
            android:layout_marginTop="4dp"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_expiration_date"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_normal_price"
                android:layout_width="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tv_normal_price_discount"
                app:layout_constraintHorizontal_bias="0"
                android:layout_marginStart="6dp"
                android:textSize="11dp"
                android:layout_marginEnd="5dp"
                android:textStyle="bold"
                android:textColor="@color/color_ff2121"
                android:maxLines="1"
                tools:text="￥29.00xxxxxxxxxx"
                app:layout_constraintHorizontal_chainStyle="packed"
                android:layout_marginHorizontal="5dp"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_height="wrap_content"/>

            <TextView
                android:id="@+id/tv_normal_price_discount"
                android:layout_width="wrap_content"
                android:textColor="@color/color_ff2121"
                android:textSize="10dp"
                app:layout_constraintStart_toEndOf="@id/tv_normal_price"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constrainedWidth="true"
                app:layout_constraintBaseline_toBaselineOf="@id/tv_normal_price"
                android:maxLines="1"
                android:ellipsize="end"
                tools:text="折后约 ¥222.29"
                android:layout_height="wrap_content"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--拼团商品 价格-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_teamwork_price"
            android:layout_width="0dp"
            android:layout_marginTop="4dp"
            android:layout_height="wrap_content"
            android:visibility="visible"
            tools:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_expiration_date">

            <ImageView
                android:id="@+id/iv_pingtuan"
                android:layout_width="31dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_marginStart="6dp"
                android:src="@drawable/icon_home_pintuan"
                android:layout_height="20dp"/>
            
            <TextView
                android:id="@+id/tv_teamwork_price"
                android:layout_width="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tv_teamwork_price_discount"
                android:textSize="11dp"
                android:textStyle="bold"
                android:textColor="@color/color_ff2121"
                app:layout_constraintHorizontal_bias="0"
                android:layout_marginEnd="5dp"
                app:layout_constraintHorizontal_chainStyle="packed"
                android:layout_marginStart="38dp"
                tools:text="￥29.90xxxxxxxxxxxxxxxxxxxxxxxx"
                android:maxLines="1"
                android:layout_height="wrap_content"/>

            <TextView
                android:id="@+id/tv_teamwork_price_discount"
                android:layout_width="wrap_content"
                android:textColor="@color/color_ff2121"
                android:textSize="10dp"
                app:layout_constraintStart_toEndOf="@id/tv_teamwork_price"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constrainedWidth="true"
                app:layout_constraintBaseline_toBaselineOf="@id/tv_teamwork_price"
                android:maxLines="1"
                android:ellipsize="end"
                tools:text="折后约 ¥222.29"
                android:layout_height="wrap_content"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--单品包邮 价格-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_free_shipping_price"
            android:layout_width="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="4dp"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/tv_expiration_date"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_free_shipping"
                android:text="包邮价"
                android:textSize="11dp"
                android:textColor="@color/color_ff2121"
                android:layout_width="wrap_content"
                app:layout_constraintBaseline_toBaselineOf="@id/tv_free_shipping_price"
                android:gravity="center"
                android:layout_marginStart="6dp"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_height="wrap_content"/>

            <TextView
                android:id="@+id/tv_free_shipping_price"
                android:layout_width="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_free_shipping"
                app:layout_constraintEnd_toStartOf="@id/tv_free_shipping_price_discount"
                app:layout_constraintHorizontal_bias="0"
                android:textSize="11dp"
                android:textStyle="bold"
                android:textColor="@color/color_ff2121"
                tools:text="￥29.00xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                app:layout_constraintHorizontal_chainStyle="packed"
                android:layout_marginEnd="5dp"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_height="wrap_content"/>

            <TextView
                android:id="@+id/tv_free_shipping_price_discount"
                android:layout_width="wrap_content"
                android:textColor="@color/color_ff2121"
                android:textSize="10dp"
                app:layout_constraintStart_toEndOf="@id/tv_free_shipping_price"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constrainedWidth="true"
                app:layout_constraintBaseline_toBaselineOf="@id/tv_free_shipping_price"
                android:maxLines="1"
                android:ellipsize="end"
                tools:text="折后约 ¥222.29"
                android:layout_height="wrap_content"/>

        </androidx.constraintlayout.widget.ConstraintLayout>
        
        <!--秒杀商品 价格-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_flash_sale_price"
            android:layout_width="0dp"
            android:layout_marginTop="4dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/tv_expiration_date"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_flash_sale"
                android:text="秒杀价"
                android:textSize="11dp"
                android:textColor="@color/color_ff2121"
                android:layout_width="wrap_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBaseline_toBaselineOf="@id/tv_flash_sale_price"
                app:layout_constraintHorizontal_bias="0"
                android:gravity="center"
                android:layout_marginStart="6dp"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_height="0dp"/>

            <TextView
                android:id="@+id/tv_flash_sale_price"
                android:layout_width="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toEndOf="@id/tv_flash_sale"
                app:layout_constraintEnd_toStartOf="@id/tv_flash_sale_price_discount"
                android:textSize="15dp"
                android:textStyle="bold"
                android:textColor="@color/color_ff2121"
                tools:text="￥29.00xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                android:layout_marginEnd="5dp"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_height="wrap_content"/>

            <TextView
                android:id="@+id/tv_flash_sale_price_discount"
                android:layout_width="wrap_content"
                android:textColor="@color/color_ff2121"
                android:textSize="10dp"
                app:layout_constraintStart_toEndOf="@id/tv_flash_sale_price"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constrainedWidth="true"
                android:maxLines="1"
                app:layout_constraintBaseline_toBaselineOf="@id/tv_flash_sale_price"
                android:ellipsize="end"
                tools:text="折后约 ¥222.29"
                android:layout_height="wrap_content"/>

        </androidx.constraintlayout.widget.ConstraintLayout>


        <!--价格不可见 价格-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_cant_see_price"
            android:layout_width="0dp"
            android:layout_marginTop="4dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/tv_expiration_date"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_cant_see_price"
                android:layout_width="0dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:textSize="14dp"
                android:textStyle="bold"
                android:textColor="@color/color_ff2121"
                tools:text="价格登录可见"
                android:layout_marginHorizontal="5dp"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_height="wrap_content" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--barrier阻拦 让标签在所有不同价格布局的下面-->
        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="cl_normal_price,cl_cant_see_price,cl_flash_sale_price,cl_free_shipping_price,cl_teamwork_price"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_expiration_date" />

        <!-- 单价显示 -->
        <TextView
            android:id="@+id/tv_unit_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginVertical="4dp"
            android:layout_marginStart="6dp"
            android:layout_marginEnd="8dp"
            android:background="#F5F5F5"
            android:paddingHorizontal="6dp"
            android:paddingVertical="2dp"
            android:textColor="#666666"
            android:textSize="@dimen/dimen_dp_10"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/barrier2"
            tools:text="约¥6.67/kg"
            tools:visibility="visible" />

        <com.ybmmarket20.view.ShopNameWithTagView
            android:id="@+id/tag_view"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_2"
            android:layout_marginEnd="@dimen/dimen_dp_20"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginStart="5dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_unit_price" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
