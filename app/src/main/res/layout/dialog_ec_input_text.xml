<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rl_outside_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <LinearLayout
        android:id="@+id/rl_inputdlg_view"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_alignParentBottom="true"
        android:background="@color/color_input_dialog_background"
        android:orientation="horizontal">

        <EditText
            android:id="@+id/et_input_message"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="4"
            android:hint="@string/dialog_input_text_hint"
            android:imeOptions="actionUnspecified"
            android:inputType="text"
            android:maxLength="30"
            android:maxLines="1"
            android:paddingStart="15dp"
            android:singleLine="true"
            android:textColor="@color/text_292933"
            android:textSize="14dp" />


        <LinearLayout
            android:id="@+id/confirm_area"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginEnd="10dp">

            <Button
                android:id="@+id/confrim_btn"
                android:layout_width="44dp"
                android:layout_height="25dp"
                android:background="@drawable/btn_send_message"
                android:gravity="center"
                android:text="@string/send"
                android:textColor="@color/colorTextWhite"
                android:textSize="12dp" />
        </LinearLayout>


    </LinearLayout>

</RelativeLayout>