<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rl_timing"
    android:layout_width="match_parent"
    android:layout_height="65dp"
    android:background="@drawable/bg_detail_time_layout"
    android:gravity="center_vertical"
    android:minHeight="60dp"
    android:visibility="gone"
    tools:visibility="visible">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center_vertical">

        <!-- 第一行：秒杀价格 -->
        <LinearLayout
            android:id="@+id/ll_first_line_promotion"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:baselineAligned="true"
            android:gravity="bottom"
            android:layout_marginLeft="10dp">

            <TextView
                android:id="@+id/tv_tax_amount_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="¥15.25"
                android:textColor="@color/white"
                android:textSize="25sp"
                android:textStyle="bold"
                android:gravity="bottom"
                android:layout_gravity="bottom" />

            <TextView
                android:id="@+id/tv_tax_discount_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dimen_dp_4"
                android:drawableRight="@drawable/icon_product_detail_tips"
                android:drawablePadding="3dp"
                android:textColor="@color/white"
                android:textSize="@dimen/dimen_dp_12"
                android:visibility="gone"
                android:gravity="bottom"
                android:layout_gravity="bottom"
                tools:text="折后价 ¥12.50"
                tools:visibility="visible" />

        </LinearLayout>

        <!--第二行：单价和建议零售价-->
        <LinearLayout
            android:id="@+id/ll_second_line_promotion"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/ll_first_line_promotion"
            android:layout_marginLeft="10dp"
            android:orientation="horizontal"
            android:visibility="gone"
            tools:visibility="visible">

            <!-- 单价显示 -->
            <TextView
                android:id="@+id/tv_promotion_unit_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:paddingLeft="4dp"
                android:paddingRight="4dp"
                android:paddingTop="5dp"
                android:paddingBottom="2dp"
                android:visibility="gone"
                tools:text="单价"
                tools:visibility="visible" />

            <!-- 分隔线 -->
            <View
                android:id="@+id/view_promotion_divider"
                android:layout_width="1dp"
                android:layout_height="12dp"
                android:background="@color/white"
                android:layout_marginLeft="6dp"
                android:layout_marginRight="6dp"
                android:layout_gravity="center_vertical"
                android:visibility="gone" />

            <!--建议零售价-->
            <RelativeLayout
                android:id="@+id/rl_suggested_retail_price_03"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tv_layout_11_02"
                    style="@style/commodity_tv_style_02"
                    android:layout_width="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    tools:text="@string/detail_tv_suggested_retail_price" />

                <TextView
                    android:id="@+id/tv_suggested_retail_price_03"
                    style="@style/commodity_tv_style"
                    android:layout_width="wrap_content"
                    android:layout_toRightOf="@+id/tv_layout_11_02"
                    android:textColor="@color/white"
                    android:textSize="12sp" />
            </RelativeLayout>

        </LinearLayout>

    </RelativeLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true"
        android:background="@color/white"
        android:gravity="center"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="#4DFF982C"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingLeft="12dp"
            android:paddingRight="12dp">

            <TextView
                android:id="@+id/tv_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_dp_8"
                android:text="距离结束仅剩"
                android:textColor="@color/color_292933"
                android:textSize="12sp" />

            <LinearLayout
                android:id="@+id/ll_time"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="invisible"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tv_day"
                    style="@style/home_seckill_style_02"
                    android:text="12" />

                <TextView
                    android:id="@+id/tv_dot_day"
                    style="@style/home_seckill_point_02"
                    android:text=":" />

                <TextView
                    android:id="@+id/tv_hour"
                    style="@style/home_seckill_style_02"
                    android:text="12" />

                <TextView
                    android:id="@+id/tv_dot_hour"
                    style="@style/home_seckill_point_02"
                    android:text=":" />

                <TextView
                    android:id="@+id/tv_minute"
                    style="@style/home_seckill_style_02"
                    android:text="50" />

                <TextView
                    style="@style/home_seckill_point_02"
                    android:text=":" />

                <TextView
                    android:id="@+id/tv_second"
                    style="@style/home_seckill_style_02"
                    android:text="50" />
            </LinearLayout>
        </LinearLayout>

    </LinearLayout>

</RelativeLayout>