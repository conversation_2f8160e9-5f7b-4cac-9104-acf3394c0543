<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:background="@color/white"
              android:orientation="vertical">

    <include layout="@layout/common_header_items"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            style="@style/invoice_information_ll_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                style="@style/invoice_information_ll2_style"
                android:layout_width="match_parent"
                android:layout_height="40dp">

                <TextView
                    android:id="@+id/tv_title_class"
                    style="@style/invoice_information_tv_style"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:drawableRight="@drawable/icon_tv_invoice_information"
                    android:text="发票类型"/>

            </LinearLayout>

            <LinearLayout
                style="@style/invoice_information_ll3_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_zzs_title"
                    style="@style/invoice_information_tv2_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:text="电子普通发票"/>

                <TextView
                    android:id="@+id/tv_zzs_text"
                    style="@style/invoice_information_tv3_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:text=""/>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            style="@style/invoice_information_ll_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                style="@style/invoice_information_ll2_style"
                android:layout_width="match_parent"
                android:layout_height="40dp">

                <TextView
                    android:id="@+id/tv_fp_title"
                    style="@style/invoice_information_tv_style"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="发票抬头"/>

            </LinearLayout>

            <LinearLayout
                style="@style/invoice_information_ll3_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_fp_gs"
                    style="@style/invoice_information_tv2_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="公司"/>

                <TextView
                    android:id="@+id/tv_fp_ydmc"
                    style="@style/invoice_information_tv3_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="药店名称:"/>

                <TextView
                    android:id="@+id/tv_fp_nsrsbh"
                    style="@style/invoice_information_tv3_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:text="纳税人识别号:"/>
            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/tv_fp_memo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:gravity="center_horizontal"
            android:paddingLeft="17dp"
            android:paddingRight="17dp"
            android:paddingTop="13dp"
            android:text=""/>
    </LinearLayout>

</LinearLayout>