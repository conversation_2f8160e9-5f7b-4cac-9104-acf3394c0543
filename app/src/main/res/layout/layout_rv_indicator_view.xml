<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <View
        android:id="@+id/view_indicator_layout"
        android:layout_width="30dp"
        android:layout_height="2dp"
        android:background="@drawable/indicator_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/view_indicator"
        android:layout_width="20dp"
        android:layout_height="2dp"
        android:background="@drawable/indicator_before"
        app:layout_constraintLeft_toLeftOf="@id/view_indicator_layout"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
