<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_69"
        android:layout_marginStart="@dimen/dimen_dp_3"
        android:layout_marginEnd="@dimen/dimen_dp_3"
        android:layout_marginBottom="@dimen/dimen_dp_2"
        android:background="@drawable/icon_audit_status_float_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/iv_audit_float_view"
            android:layout_width="@dimen/dimen_dp_38"
            android:layout_height="@dimen/dimen_dp_38"
            android:layout_marginStart="@dimen/dimen_dp_17"
            android:src="@drawable/icon_audit_float_view"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_audit_status_float_text"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_dp_5"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_15"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/iv_audit_float_view"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/cl_audit_status_float_btn"
            android:layout_marginEnd="@dimen/dimen_dp_5"
            android:text="@string/no_audit_passed" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_audit_status_float_btn"
            android:layout_width="@dimen/dimen_dp_79"
            android:layout_height="@dimen/dimen_dp_30"
            android:background="@drawable/shape_audit_status_home_float_btn"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginEnd="@dimen/dimen_dp_17"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_audit_status_home_float_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="资质认证"
                android:drawableRight="@drawable/icon_audit_status_float_btn"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:textColor="@color/white"
                android:textSize="@dimen/dimen_dp_12" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>