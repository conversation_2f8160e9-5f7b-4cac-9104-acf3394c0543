<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_dp_60">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="@dimen/dimen_dp_21"
        android:layout_height="@dimen/dimen_dp_21"
        android:src="@drawable/icon_service_type_money"
        android:layout_marginStart="@dimen/dimen_dp_13"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_14"
        android:layout_marginTop="@dimen/dimen_dp_10"
        android:layout_marginStart="@dimen/dimen_dp_13"
        android:text="退款（无需退货）"
        app:layout_constraintStart_toEndOf="@+id/iv_icon"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_sub_title"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:textColor="@color/color_9494A6"
        android:textSize="@dimen/dimen_dp_12"
        android:layout_marginStart="@dimen/dimen_dp_13"
        android:layout_marginBottom="@dimen/dimen_dp_10"
        android:text="没收到货，或与卖家协商同意不用退货只退款"
        android:singleLine="true"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/iv_more_arrow"
        app:layout_constraintStart_toEndOf="@+id/iv_icon" />

    <ImageView
        android:id="@+id/iv_more_arrow"
        android:layout_width="@dimen/dimen_dp_15"
        android:layout_height="@dimen/dimen_dp_15"
        android:src="@drawable/icon_service_type_more_arrow"
        android:layout_marginEnd="@dimen/dimen_dp_5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>