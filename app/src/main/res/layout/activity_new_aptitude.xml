<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_aptitude_bg"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <com.flyco.tablayout.SlidingTabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/white"
        app:tl_indicator_color="@color/color_theme_base_color"
        app:tl_indicator_corner_radius="2dp"
        app:tl_indicator_height="3dp"
        app:tl_indicator_margin_bottom="0dp"
        app:tl_indicator_width="34dp"
        app:tl_indicator_width_equal_title="false"
        app:tl_tab_padding="13dp"
        app:tl_tab_space_equal="true"
        app:tl_textBold="SELECT"
        app:tl_textSelectColor="@color/color_text_base_color"
        app:tl_textSelectSize="17dp"
        app:tl_textUnselectColor="@color/color_text_base_color"
        app:tl_textsize="15dp" />

    <View style="@style/normal_horizontal_line" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <LinearLayout
        android:id="@+id/ll_bottom"
        android:layout_width="match_parent"
        android:layout_height="50dp">

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_download"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="药帮忙资质下载"
            android:textColor="@color/color_text_base_color"
            android:textSize="16dp"
            android:visibility="gone"
            app:rv_backgroundColor="@color/white"
            app:rv_cornerRadius="2dp" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_operation"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="添加首营资质"
            android:textColor="@color/white"
            android:textSize="16dp"
            app:rv_backgroundColor="@color/color_theme_base_color"
            app:rv_cornerRadius="2dp" />

    </LinearLayout>

</LinearLayout>
