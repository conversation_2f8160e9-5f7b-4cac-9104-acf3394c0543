<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_privacy"
        android:paddingBottom="15dp">

        <TextView
            android:id="@+id/tv_privacydialog_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="25dp"
            android:textColor="#292933"
            android:textSize="18dp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="dsafas" />


        <TextView
            android:id="@+id/tv_privacydialog_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="25dp"
            android:gravity="start"
            android:lineSpacingExtra="7dp"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:textColor="#676773"
            android:textSize="14dp"
            app:layout_constraintTop_toBottomOf="@id/tv_privacydialog_title"
            tools:text="dsafadsafsdahfkasdfhsdhfksahdfkasdfhashfhkashfasdjfjhasfhsdfjkasdfhasjkfdhsafhasdfhsjkdfshdjfhsjdfkjasjkdhfkjsadhjkfhsdakjhfkjsdkjfjkhdsajfhs" />


        <TextView
            android:id="@+id/tv_privacydialog_disagree"
            style="@style/Dialog_PrivacyBtnStyle"
            android:layout_marginLeft="15dp"
            android:background="@drawable/bg_dialog_unpress_btn_privacy"
            android:text="@string/disagree"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/tv_privacydialog_agree"
            app:layout_constraintTop_toBottomOf="@id/tv_privacydialog_content"
            tools:text="dsafas" />

        <TextView
            android:id="@+id/tv_privacydialog_agree"
            style="@style/Dialog_PrivacyBtnStyle"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:background="@drawable/bg_dialog_press_btn_privacy"
            android:text="@string/agree"
            android:textColor="@color/white"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintLeft_toRightOf="@id/tv_privacydialog_disagree"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_privacydialog_content"
            tools:text="dsafas" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</ScrollView>