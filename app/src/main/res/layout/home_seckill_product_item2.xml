<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="110dp"
    android:background="@color/white"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:paddingBottom="8dp"
    android:paddingLeft="4dp"
    android:paddingRight="4dp"
    android:paddingTop="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <FrameLayout
            android:layout_width="102dp"
            android:layout_height="102dp">

            <ImageView
                android:id="@+id/iv_product"
                android:layout_width="102dp"
                android:layout_height="102dp"
                android:layout_marginTop="2dp"
                android:padding="@dimen/home_product_image_padding" />

            <ImageView
                android:id="@+id/iv_tag_left"
                android:layout_width="102dp"
                android:layout_height="102dp"
                android:layout_marginTop="2dp"
                android:scaleType="fitXY"
                android:src="@drawable/transparent" />

            <TextView
                android:id="@+id/tv_activity_price"
                style="@style/activity_price"
                android:layout_height="12dp"
                android:text=""
                android:visibility="gone" />

        </FrameLayout>

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:gravity="center_vertical"
            android:singleLine="true"
            android:text=""
            android:textColor="#ff000000"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/tv_spec"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:singleLine="true"
            android:text=""
            android:textColor="@color/text_9494A6"
            android:textSize="12sp" />

        <LinearLayout
            android:id="@+id/ll_price_parent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:lines="1"
                android:paddingBottom="2dp"
                android:paddingTop="2dp"
                android:singleLine="true"
                android:text=""
                android:textColor="@color/record_red"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_old"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:lines="1"
                android:paddingBottom="2dp"
                android:paddingLeft="4dp"
                android:paddingTop="2dp"
                android:singleLine="true"
                android:text=""
                android:textColor="@color/text_9494A6"
                android:textSize="13sp" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_audit_passed_visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="价格认证资质可见"
            android:textColor="@color/detail_tv_FF982C"
            android:layout_marginTop="@dimen/dimen_dp_7"
            android:textSize="@dimen/dimen_dp_12" />

        <com.ybmmarket20.view.TagView
            android:id="@+id/rl_icon_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

        </com.ybmmarket20.view.TagView>
    </LinearLayout>

    <TextView
        android:id="@+id/tv_state"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_gravity="center"
        android:layout_marginBottom="35dp"
        android:background="@drawable/shop_limit01"
        android:gravity="center"
        android:text=""
        android:textColor="#ffffff"
        android:textSize="12dp"
        android:visibility="visible" />

</FrameLayout>