<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/color_F7F7F7"
    android:orientation="vertical">

    <include
        android:id="@+id/header"
        layout="@layout/common_header_items" />

    <com.ybmmarket20.common.widget.RoundConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_78"
        android:layout_marginTop="@dimen/dimen_dp_10"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius_TL="@dimen/dimen_dp_6"
        app:rv_cornerRadius_TR="@dimen/dimen_dp_6">

        <ImageView
            android:id="@+id/ivLogo"
            android:layout_width="@dimen/dimen_dp_44"
            android:layout_height="@dimen/dimen_dp_44"
            android:layout_marginStart="@dimen/dimen_dp_15"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:src="@drawable/logo"/>

        <TextView
            android:id="@+id/tvBankName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="建设银行"
            app:layout_constraintTop_toTopOf="@+id/ivLogo"
            app:layout_constraintStart_toEndOf="@+id/ivLogo"
            android:layout_marginStart="@dimen/dimen_dp_14"
            android:textColor="#292933"
            android:textSize="@dimen/dimen_dp_16"
            android:textStyle="bold"
            android:layout_marginTop="@dimen/dimen_dp_4"/>

        <TextView
            android:id="@+id/tvBankCardType"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="储蓄卡"
            app:layout_constraintStart_toEndOf="@+id/ivLogo"
            app:layout_constraintBottom_toBottomOf="@+id/ivLogo"
            android:layout_marginStart="@dimen/dimen_dp_14"
            android:textColor="#676773"
            android:textSize="@dimen/dimen_dp_12" />

        <TextView
            android:id="@+id/tvBankCardNum"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:text="4399"
            android:textColor="#292933"
            android:textStyle="bold"
            android:layout_marginEnd="@dimen/dimen_dp_15"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toStartOf="@+id/tvBankCardNum"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:text="****"
            android:textColor="#979799"
            android:textStyle="bold"
            android:layout_marginEnd="@dimen/dimen_dp_7"/>


    </com.ybmmarket20.common.widget.RoundConstraintLayout>
    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/rtvUnBind"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_40"
        android:text="解绑银行卡"
        android:gravity="center"
        android:textSize="@dimen/dimen_dp_14"
        android:textColor="@color/color_ff2121"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_1"
        app:rv_cornerRadius_BL="@dimen/dimen_dp_6"
        app:rv_cornerRadius_BR="@dimen/dimen_dp_6"
        app:rv_backgroundColor="@color/white" />

</LinearLayout>