<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colors_f5f5f5"
    android:orientation="vertical">


    <RadioGroup
        android:id="@+id/rg_tab"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_40"
        android:background="@color/white"
        android:orientation="horizontal">

        <RadioButton
            android:id="@+id/rb_defalt"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:button="@null"
            android:checked="true"
            android:gravity="center"
            android:text="默认"
            android:textColor="@color/selector_text_color_shop_list"
            android:textSize="@dimen/dimen_dp_13" />

        <RadioButton
            android:id="@+id/rb_new"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:button="@null"
            android:checked="false"
            android:gravity="center"
            android:text="最新入驻"
            android:textColor="@color/selector_text_color_shop_list"
            android:textSize="@dimen/dimen_dp_13" />

    </RadioGroup>


    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/smartrefresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ll_title">

        <com.scwang.smart.refresh.header.ClassicsHeader
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_shop_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:listitem="@layout/item_shop_list" />


    </com.scwang.smart.refresh.layout.SmartRefreshLayout>


</LinearLayout>