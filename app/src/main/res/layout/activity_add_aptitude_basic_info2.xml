<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/white"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical"
    tools:context="com.ybmmarket20.activity.AddAptitudeBasicInfoActivity">

    <include layout="@layout/common_header_items" />

    <LinearLayout
        android:id="@+id/ll_top_tips"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#FFF7EF"
        android:paddingTop="9dp"
        android:paddingBottom="9dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            android:text="@string/str_aptitude_add_basic_info_top_tips"
            android:textColor="#99664D"
            android:textSize="14sp" />

        <ImageView
            android:id="@+id/iv_top_tips_delete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="top"
            android:paddingStart="10dp"
            android:paddingTop="5dp"
            android:paddingEnd="10dp"
            android:paddingBottom="10dp"
            android:src="@drawable/icon_add_aptitude_basic_info_top_close" />

    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.ybmmarket20.view.AptitudeProgressView
                android:id="@+id/apv"
                android:layout_marginTop="@dimen/dimen_dp_15"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:orientation="horizontal"
                tools:itemCount="3"
                app:layoutManager="com.ybm.app.view.WrapGridLayoutManager"
                tools:listitem="@layout/item_aptitude_pregress"
                tools:layout_height="@dimen/dimen_dp_50" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="25dp"
                android:text="@string/str_aptitude_add_basic_info_company_tips"
                android:textColor="@color/text_292933"
                android:textSize="15sp"
                android:textStyle="bold" />

            <LinearLayout
                android:id="@+id/ll_authentication"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="visible"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.8dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginTop="15dp"
                    android:background="@color/colors_f5f5f5" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/tv_type_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="20dp"
                        android:text="@string/str_aptitude_add_basic_info_company_type_tips2"
                        android:textColor="@color/text_292933"
                        android:textSize="14sp" />

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/et_type"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@null"
                        android:drawableEnd="@drawable/icon_aptitude_add_basic_info_arrow"
                        android:focusable="false"
                        android:gravity="center_vertical"
                        android:hint="@string/str_aptitude_add_basic_info_company_type_hint"
                        android:inputType="text"
                        android:paddingEnd="10dp"
                        android:singleLine="true"
                        android:textColor="#292933"
                        android:textColorHint="@color/text_9494A6"
                        android:textSize="14sp" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.8dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:background="@color/colors_f5f5f5" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:minHeight="@dimen/dimen_dp_44"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/tv_code_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="20dp"
                        android:text="@string/str_aptitude_add_basic_info_company_name_code2"
                        android:textColor="@color/text_292933"
                        android:textSize="14sp" />

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/et_code"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@null"
                        android:gravity="center_vertical"
                        android:hint="@string/str_aptitude_add_basic_info_company_code_hint"
                        android:inputType="text"
                        android:maxLength="50"
                        android:singleLine="true"
                        android:textColor="#292933"
                        android:textColorHint="@color/text_9494A6"
                        android:textCursorDrawable="@drawable/shape_edit_cursor_drawable"
                        android:textSize="14sp" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.8dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:background="@color/colors_f5f5f5" />
                
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="为保证结果准确性，请保证企业类型和证照编码与实际相符"
                    android:textSize="@dimen/dimen_dp_12"
                    android:background="@color/color_fff7ef"
                    android:layout_marginStart="@dimen/dimen_dp_10"
                    android:layout_marginEnd="@dimen/dimen_dp_10"
                    android:layout_marginTop="@dimen/dimen_dp_10"
                    android:padding="@dimen/dimen_dp_10"
                    android:textColor="@color/colors_99664D" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_userInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.8dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:background="@color/colors_f5f5f5" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="20dp"
                        android:text="@string/str_aptitude_add_basic_info_company_name_tips"
                        android:textColor="@color/text_292933"
                        android:textSize="14sp" />

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/et_name"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@null"
                        android:gravity="center_vertical"
                        android:hint="@string/str_aptitude_add_basic_info_company_name_hint"
                        android:inputType="text"
                        android:maxLength="50"
                        android:singleLine="true"
                        android:textColor="#292933"
                        android:textColorHint="@color/text_9494A6"
                        android:textCursorDrawable="@drawable/shape_edit_cursor_drawable"
                        android:textSize="14sp" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.8dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:background="@color/colors_f5f5f5" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="20dp"
                        android:text="@string/str_aptitude_add_basic_info_area_address_tips"
                        android:textColor="@color/text_292933"
                        android:textSize="14sp" />

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/et_address"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@null"
                        android:cursorVisible="false"
                        android:drawableEnd="@drawable/icon_aptitude_add_basic_info_arrow"
                        android:focusable="false"
                        android:gravity="center_vertical"
                        android:hint="@string/str_aptitude_add_basic_info_area_address_hint"
                        android:inputType="text"
                        android:paddingEnd="10dp"
                        android:singleLine="true"
                        android:textColor="#292933"
                        android:textColorHint="@color/text_9494A6"
                        android:textSize="14sp" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.8dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:background="@color/colors_f5f5f5" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="44dp"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="20dp"
                        android:gravity="center"
                        android:text="@string/str_aptitude_add_basic_info_detail_address_tips"
                        android:textColor="@color/text_292933"
                        android:textSize="14sp" />

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/et_detail_address"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="45dp"
                        android:background="@null"
                        android:hint="@string/str_aptitude_add_basic_info_detail_address_hint"
                        android:inputType="textMultiLine"
                        android:maxLength="200"
                        android:paddingBottom="20dp"
                        android:textColor="#292933"
                        android:textColorHint="@color/text_9494A6"
                        android:textCursorDrawable="@drawable/shape_edit_cursor_drawable"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_invoice_info_container"
                    android:layout_width="match_parent"
                    tools:visibility="visible"
                    android:visibility="gone"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.8dp"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:background="@color/colors_f5f5f5" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="44dp"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="20dp"
                            android:text="@string/str_aptitude_add_basic_info_invoice_type_tips"
                            android:textColor="@color/text_292933"
                            android:textSize="14sp" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/et_invoice_type"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@null"
                            android:drawableEnd="@drawable/icon_aptitude_add_basic_info_arrow"
                            android:focusable="false"
                            android:gravity="center_vertical"
                            android:inputType="text"
                            tools:text="电子普通发票"
                            android:paddingEnd="10dp"
                            android:singleLine="true"
                            android:textColor="#292933"
                            android:textColorHint="@color/text_9494A6"
                            android:textSize="14sp" />
                    </LinearLayout>
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.8dp"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:background="@color/colors_f5f5f5" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </ScrollView>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_77">

        <com.ybmmarket20.view.ButtonObserver
            android:id="@+id/btn_submit"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:layout_marginBottom="15dp"
            android:background="@drawable/selector_common_btn"
            android:enabled="false"
            android:gravity="center"
            android:text="@string/next_step1"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <com.ybmmarket20.view.ButtonObserver
            android:id="@+id/btn_submit_authentication"
            android:layout_width="match_parent"
            android:visibility="gone"
            android:layout_height="44dp"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:layout_marginBottom="15dp"
            android:background="@drawable/selector_common_btn"
            android:enabled="false"
            android:gravity="center"
            android:text="信息认证"
            android:textColor="@color/white"
            android:textSize="16sp" />

    </RelativeLayout>
</LinearLayout>
