<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_f7f7f8">

    <com.ybm.app.view.CommonRecyclerView
        android:id="@+id/crv_refresh_common"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/v_channel_navigation_bg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_64"
        android:alpha="0"
        android:background="@color/white"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_back_channel"
        android:layout_width="@dimen/dimen_dp_22"
        android:layout_height="@dimen/dimen_dp_22"
        android:layout_marginLeft="@dimen/dimen_dp_12"
        android:layout_marginBottom="@dimen/dimen_dp_9"
        android:src="@drawable/icon_navigateion_back_white"
        app:layout_constraintBottom_toBottomOf="@+id/v_channel_navigation_bg"
        app:layout_constraintStart_toStartOf="@+id/v_channel_navigation_bg" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="@dimen/dimen_dp_17"
        app:layout_constraintBottom_toBottomOf="@+id/iv_back_channel"
        app:layout_constraintEnd_toEndOf="@+id/v_channel_navigation_bg"
        app:layout_constraintStart_toStartOf="@+id/v_channel_navigation_bg"
        app:layout_constraintTop_toTopOf="@+id/iv_back_channel"
        tools:text="慢病用药" />


</androidx.constraintlayout.widget.ConstraintLayout>