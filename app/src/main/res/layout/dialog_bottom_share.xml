<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#ffffff"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:layout_marginTop="20dp">

            <View
                android:layout_width="90dp"
                android:layout_height="0.5dp"
                android:layout_centerVertical="true"
                android:layout_marginRight="4dp"
                android:layout_toLeftOf="@+id/tv_center_tip"
                android:background="#cccccc" />

            <TextView
                android:id="@+id/tv_center_tip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:gravity="center"
                android:text="转发到"
                android:textColor="@color/color_292933"
                android:textSize="17sp" />

            <View
                android:layout_width="90dp"
                android:layout_height="0.5dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="4dp"
                android:layout_toRightOf="@+id/tv_center_tip"
                android:background="#cccccc" />
        </RelativeLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="10dp"
            android:layout_marginTop="10dp">

            <LinearLayout
                android:id="@+id/ll_share_wx"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:src="@drawable/icon_share_wx" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="2dp"
                    android:text="微信"
                    android:textColor="@color/text_9494A6"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_share_qq"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="55dp"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:src="@drawable/icon_share_qq" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="2dp"
                    android:text="QQ"
                    android:textColor="@color/text_9494A6"
                    android:textSize="12sp" />
            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_margin="16dp"
            android:background="@drawable/bg_radio_rect_border_gray"
            android:gravity="center"
            android:text="取消"
            android:textColor="@color/color_292933"
            android:textSize="17sp" />
    </LinearLayout>
</LinearLayout>