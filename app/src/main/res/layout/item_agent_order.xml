<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/dimen_dp_10"
    android:layout_marginTop="@dimen/dimen_dp_10"
    android:layout_marginEnd="@dimen/dimen_dp_10"
    android:background="@drawable/shape_item_agent_order">

    <LinearLayout
        android:id="@+id/ll_agent_order_top"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_40"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_agent_order_No"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_weight="1"
            android:singleLine="true"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_15"
            tools:text="订单编号：ybm103088888" />

        <LinearLayout
            android:id="@+id/ll_tv_agent_order_skip"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_agent_order_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dimen_dp_10"
                android:textColor="@color/color_00B377"
                android:textSize="@dimen/dimen_dp_12"
                tools:text="待确认" />

            <ImageView
                android:layout_width="@dimen/dimen_dp_6"
                android:layout_height="@dimen/dimen_dp_11"
                android:layout_marginStart="@dimen/dimen_dp_6"
                android:layout_marginEnd="@dimen/dimen_dp_10"
                android:src="@drawable/promotion_right_arrow"
                android:visibility="gone" />
        </LinearLayout>
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:background="@color/colors_f5f5f5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_agent_order_top" />

    <ImageView
        android:id="@+id/iv_agent_order"
        android:layout_width="@dimen/dimen_dp_75"
        android:layout_height="@dimen/dimen_dp_75"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_agent_order_top" />

    <TextView
        android:id="@+id/tv_agent_order_goods_count"
        android:layout_width="@dimen/dimen_dp_75"
        android:layout_height="@dimen/dimen_dp_15"
        android:background="@color/color_4D000000"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/dimen_dp_12"
        app:layout_constraintBottom_toBottomOf="@+id/iv_agent_order"
        app:layout_constraintEnd_toEndOf="@+id/iv_agent_order"
        app:layout_constraintStart_toStartOf="@+id/iv_agent_order"
        tools:text="共3件商品" />

    <TextView
        android:id="@+id/tv_agent_order_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_23"
        android:layout_marginTop="@dimen/dimen_dp_3"
        android:text="@string/agent_order_price"
        app:layout_constraintStart_toEndOf="@+id/iv_agent_order"
        app:layout_constraintTop_toTopOf="@+id/iv_agent_order" />

    <TextView
        android:id="@+id/tv_agent_order_price_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_13"
        app:layout_constraintStart_toEndOf="@+id/tv_agent_order_price"
        app:layout_constraintTop_toTopOf="@+id/tv_agent_order_price"
        tools:text="¥123.98" />

    <TextView
        android:id="@+id/tv_agent_order_applicant"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_23"
        android:text="@string/agent_order_applicant"
        app:layout_constraintBottom_toBottomOf="@+id/iv_agent_order"
        app:layout_constraintStart_toEndOf="@+id/iv_agent_order"
        app:layout_constraintTop_toTopOf="@+id/iv_agent_order" />

    <TextView
        android:id="@+id/tv_agent_order_applicant_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_13"
        app:layout_constraintStart_toEndOf="@+id/tv_agent_order_price"
        app:layout_constraintTop_toTopOf="@+id/tv_agent_order_applicant"
        tools:text="¥123.98" />

    <TextView
        android:id="@+id/tv_agent_order_submitTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_23"
        android:layout_marginTop="@dimen/dimen_dp_9"
        android:layout_marginBottom="@dimen/dimen_dp_2"
        android:text="@string/submitTime"
        app:layout_constraintBottom_toBottomOf="@+id/iv_agent_order"
        app:layout_constraintStart_toEndOf="@+id/iv_agent_order" />

    <TextView
        android:id="@+id/tv_agent_order_submitTime_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_13"
        app:layout_constraintStart_toEndOf="@+id/tv_agent_order_price"
        app:layout_constraintTop_toTopOf="@+id/tv_agent_order_submitTime"
        tools:text="¥123.98" />

    <ImageView
        android:layout_width="@dimen/dimen_dp_6"
        android:layout_height="@dimen/dimen_dp_11"
        android:layout_marginStart="@dimen/dimen_dp_6"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:src="@drawable/promotion_right_arrow"
        app:layout_constraintBottom_toTopOf="@+id/cl_remainTime"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_agent_order_top" />

    <androidx.legacy.widget.Space
        android:id="@+id/space_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_10"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_agent_order" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_remainTime"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_33"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/space_bottom">

        <View
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_1"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginEnd="@dimen/dimen_dp_10"
            android:background="@color/colors_f5f5f5"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_remainTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:textColor="@color/color_ff2121"
            android:textSize="@dimen/dimen_dp_13"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="剩余确认时间：24:15:20" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>