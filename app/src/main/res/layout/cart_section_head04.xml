<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    android:layout_marginLeft="10dp"
    android:layout_marginRight="10dp"
    android:layout_marginBottom="0.33dp"
    android:background="#FBFBFB"
    android:orientation="horizontal">

    <LinearLayout
        android:id="@+id/cart_item_ll"
        android:layout_width="40dp"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical">

        <CheckBox
            android:id="@+id/shop_check"
            style="@style/CustomCheckboxTheme"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="9dp"
            android:gravity="center"
            android:visibility="gone" />

        <TextView
            android:id="@+id/shop_lose"
            android:layout_width="30dp"
            android:layout_height="16dp"
            android:layout_margin="6dp"
            android:background="@drawable/bg_cart_lose_icon"
            android:gravity="center"
            android:text="失效"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:visibility="visible" />
    </LinearLayout>

    <TextView
        android:textSize="15sp"
        android:textColor="#676773"
        android:text="套餐搭配"
        android:gravity="center_vertical"
        android:layout_weight="1"
        android:layout_width="0dp"
        android:layout_height="match_parent" />

</LinearLayout>