<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/my_dialog_bg"
    android:orientation="vertical"
    android:paddingTop="12dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="4dp"
        android:text="修改"
        android:textColor="@color/color_292933"
        android:textSize="17sp"
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="8dp"
            android:text="补货数量"
            android:textColor="@color/text_292933"
            android:visibility="gone" />

        <com.ybmmarket20.common.widget.RoundRelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="7.5dp"
            android:layout_marginTop="10dp"
            app:rv_backgroundColor="@color/color_f7f7f8"
            app:rv_cornerRadius="2dp">

            <EditText
                android:id="@+id/et_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toLeftOf="@+id/iv_clear"
                android:background="@null"
                android:hint="请输入补货数量"
                android:inputType="number"
                android:maxLength="7"
                android:maxLines="1"
                android:padding="8dp"
                android:selectAllOnFocus="true"
                android:singleLine="true"
                android:textColor="@android:color/black"
                android:textSize="15sp" />

            <ImageView
                android:id="@+id/iv_clear"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:scaleType="center"
                android:src="@drawable/clear_sousou"
                android:visibility="gone" />
        </com.ybmmarket20.common.widget.RoundRelativeLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="8dp"
            android:text="历史价格"
            android:textColor="@color/text_292933"
            android:visibility="gone" />

        <com.ybmmarket20.common.widget.RoundRelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:layout_marginTop="7.5dp"
            app:rv_backgroundColor="@color/color_f7f7f8"
            app:rv_cornerRadius="2dp">

            <EditText
                android:id="@+id/et_content2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toLeftOf="@+id/iv_content2_clear"
                android:background="@null"
                android:hint="请输入历史价格"
                android:maxLength="8"
                android:maxLines="1"
                android:numeric="decimal"
                android:padding="8dp"
                android:selectAllOnFocus="true"
                android:singleLine="true"
                android:textColor="@android:color/black"
                android:textSize="15sp" />

            <ImageView
                android:id="@+id/iv_content2_clear"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:scaleType="center"
                android:src="@drawable/clear_sousou"
                android:visibility="gone" />
        </com.ybmmarket20.common.widget.RoundRelativeLayout>
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="13dp"
        android:background="@color/colors_DDDDDD" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="取消"
            android:textColor="@color/color_292933"
            android:textSize="17sp" />

        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:background="@color/colors_DDDDDD" />

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="确定"
            android:textColor="@color/base_colors_new"
            android:textSize="17sp"
            android:textStyle="bold" />
    </LinearLayout>
</LinearLayout>