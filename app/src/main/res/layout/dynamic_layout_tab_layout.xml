<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!--增加自己的固定布局-->

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false"
        android:orientation="vertical">

        <com.ybmmarket20.common.widget.RoundLinearLayout
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:orientation="horizontal"
            app:rv_backgroundColor="@color/base_bg_color">

            <RadioGroup
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:paddingLeft="10dp">

                <RadioButton
                    android:id="@+id/rb_recommend"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/home_product_rb_selector_normal"
                    android:button="@null"
                    android:checked="true"
                    android:clickable="false"
                    android:gravity="center"
                    android:minWidth="80dp"
                    android:text="为你推荐"
                    android:textColor="@drawable/home_product_rb_selector_textcolor"
                    android:textSize="14sp" />

                <RadioButton
                    android:id="@+id/rb_purchase_history"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/home_product_rb_selector_normal"
                    android:button="@null"
                    android:checked="false"
                    android:clickable="false"
                    android:gravity="center"
                    android:minWidth="80dp"
                    android:text="常购清单"
                    android:textColor="@drawable/home_product_rb_selector_textcolor"
                    android:textSize="14sp" />

            </RadioGroup>

        </com.ybmmarket20.common.widget.RoundLinearLayout>

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.scwang.smart.refresh.header.ClassicsHeader
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/crv_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/base_bg_color"
                android:minHeight="400dp" />

            <com.scwang.smart.refresh.footer.ClassicsFooter
                android:id="@+id/cf"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    </LinearLayout>

</merge>