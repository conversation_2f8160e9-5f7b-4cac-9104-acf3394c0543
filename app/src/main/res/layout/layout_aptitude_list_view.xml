<?xml version="1.0" encoding="utf-8"?>
<com.ybmmarket20.common.widget.RoundLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    app:rv_backgroundColor="@color/white"
    app:rv_cornerRadius="2dp">

    <com.ybmmarket20.common.widget.RoundLinearLayout
        android:id="@+id/rl_aptitude_required_title_tops"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:divider="@color/color_line"
        android:dividerPadding="0.5dp"
        android:orientation="horizontal"
        android:showDividers="end"
        android:weightSum="10"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius_TL="2dp"
        app:rv_cornerRadius_TR="2dp">

        <TextView
            android:id="@+id/tv_aptitude"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="5"
            android:paddingLeft="17dp"
            android:text="资质名称"
            android:textColor="@color/color_text_hint_base_color"
            android:textSize="13dp" />

        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:background="@color/page_background"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_status"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="2"
            android:gravity="center_horizontal"
            android:text="资质状态"
            android:textColor="@color/color_text_hint_base_color"
            android:textSize="13dp" />

        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:background="@color/page_background"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_validity_date"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="3"
            android:gravity="center_horizontal"
            android:text="有效期至"
            android:textColor="@color/color_text_hint_base_color"
            android:textSize="13dp" />

    </com.ybmmarket20.common.widget.RoundLinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/color_divider_bg" />

    <com.ybmmarket20.common.widget.RoundLinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="2dp">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_aptitude_required"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:itemCount="4"
            tools:listitem="@layout/item_new_aptitude_required" />
    </com.ybmmarket20.common.widget.RoundLinearLayout>
</com.ybmmarket20.common.widget.RoundLinearLayout>
