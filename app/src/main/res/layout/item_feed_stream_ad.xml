<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:background="@android:color/transparent"
        android:tag="HomeFeedStreamAdapter"
        android:layout_height="wrap_content">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_ad"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:scaleType="centerCrop"
            app:shapeAppearance="@style/ShapeableImageView_10dp_rounded_corner"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="0dp"
            app:layout_constraintDimensionRatio="354:518"
            android:adjustViewBounds="true"
            android:layout_height="0dp"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
