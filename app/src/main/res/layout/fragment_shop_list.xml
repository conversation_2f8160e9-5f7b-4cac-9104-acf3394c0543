<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:background="@color/white"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <View
        android:id="@+id/status_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:background="@color/white" />

    <com.flyco.tablayout.SlidingTabLayout
        android:id="@+id/stl_shop"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginLeft="100dp"
        android:background="@color/white"
        android:layout_marginRight="100dp"
        app:tl_indicator_bounce_enable="false"
        app:tl_indicator_color="@color/home_back_selected"
        app:tl_indicator_corner_radius="2dp"
        app:tl_indicator_height="4dp"
        app:tl_indicator_width="34dp"
        app:tl_tab_space_equal="true"
        app:tl_textBold="BOTH"
        app:tl_textSelectColor="@color/text_292933"
        app:tl_textSelectSize="18dp"
        app:tl_textUnselectColor="@color/color_676773"
        app:tl_textsize="18dp" />


    <androidx.viewpager.widget.ViewPager
        android:id="@+id/vp_shop"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />


</LinearLayout>