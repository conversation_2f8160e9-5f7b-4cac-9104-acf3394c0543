<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="#E7EAEC">
    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/tv_top"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/white"
        android:gravity="center"
        app:rv_backgroundColor="@color/white"
        android:text="@string/str_common_bottom_dialog_download_to_email"
        android:textColor="@color/text_292933"
        android:textSize="16sp" />
    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/tv_bottom"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/white"
        android:layout_marginTop="1dp"
        android:gravity="center"
        app:rv_backgroundColor="@color/white"
        android:text="@string/str_common_bottom_dialog_share_to_wechat"
        android:textColor="@color/text_292933"
        android:textSize="16sp" />
    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/tv_cancel"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="15dp"
        android:background="@color/white"
        android:gravity="center"
        app:rv_backgroundColor="@color/white"
        android:text="@string/str_common_bottom_dialog_cancle"
        android:textColor="#FF515163"
        android:textSize="16sp" />
</LinearLayout>
