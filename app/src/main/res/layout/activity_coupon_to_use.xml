<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/ll_title"
        android:layout_width="match_parent"
        android:layout_height="@dimen/header_height"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/title_left_search"
            android:layout_width="54dp"
            android:layout_height="25dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_back" />

        <RelativeLayout
            android:id="@+id/rel_search"
            android:layout_width="0dp"
            android:layout_height="34dp"
            android:layout_weight="1"
            android:background="@drawable/search_round_corner_gray_bg_03"
            android:focusable="true"
            android:focusableInTouchMode="true">

            <ImageView
                android:id="@+id/iv_a_magnifying_glass"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_centerVertical="true"
                android:layout_marginStart="6dp"
                android:src="@drawable/icon_a_magnifying_glass" />

            <EditText
                android:id="@+id/et_to_use_coupon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerVertical="true"
                android:layout_marginStart="3dp"
                android:layout_toEndOf="@id/iv_a_magnifying_glass"
                android:background="@null"
                android:hint="@string/coupon_search_hint"
                android:imeOptions="actionSearch"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="@color/color_292933"
                android:textColorHint="@color/color_9494A6"
                android:textCursorDrawable="@drawable/color_cursor"
                android:textSize="@dimen/dimen_dp_13" />

            <ImageView
                android:id="@+id/iv_clear"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:src="@drawable/clear_sousou"
                android:visibility="invisible" />

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="54dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical">


            <TextView
                android:id="@+id/title_right_btn"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerInParent="true"
                android:gravity="center"
                android:text="搜索"
                android:textColor="@color/text_292933"
                android:visibility="visible" />

        </RelativeLayout>
    </LinearLayout>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/cl_coupon_to_use"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="@dimen/dimen_dp_50"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_title"
        app:layout_constraintVertical_weight="1">

        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/color_F7F7F8"
            android:fadingEdge="none"
            app:elevation="@dimen/dimen_dp_0">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_to_use_coupon_head"
                android:layout_width="match_parent"
                android:layout_height="162dp"
                android:background="@color/color_F7F7F8"
                android:paddingStart="@dimen/dimen_dp_15"
                android:paddingEnd="@dimen/dimen_dp_15"
                app:layout_scrollFlags="scroll">

                <TextView
                    android:id="@+id/tv_to_use_des"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dimen_dp_8"
                    android:text="@string/goods_coupon_scope"
                    android:textColor="@color/color_9494A6"
                    android:textSize="@dimen/dimen_dp_13"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_to_use_coupon_bg_left"
                    android:layout_width="@dimen/dimen_dp_100"
                    android:layout_height="120dp"
                    android:layout_marginTop="@dimen/dimen_dp_8"
                    android:background="@drawable/shape_item_goods_coupon_left"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_to_use_des">

                    <ImageView
                        android:layout_width="@dimen/dimen_dp_48"
                        android:layout_height="@dimen/dimen_dp_48"
                        android:src="@drawable/icon_coupon_to_use_gotten"
                        android:visibility="visible"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <LinearLayout
                        android:id="@+id/ll_coupon_amount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dimen_dp_20"
                        android:orientation="horizontal"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:id="@+id/tv_rice_unit"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/color_ff4244"
                            android:textSize="@dimen/dimen_dp_15"
                            tools:text="¥" />

                        <TextView
                            android:id="@+id/tv_coupon_amount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/color_ff4244"
                            android:textSize="@dimen/dimen_dp_32"
                            tools:text="20" />

                        <TextView
                            android:id="@+id/tv_discount_unit"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/color_ff4244"
                            android:textSize="@dimen/dimen_dp_15"
                            tools:text="折" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_coupon_full_reduce"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dimen_dp_0"
                        android:textColor="@color/color_ff4244"
                        android:textSize="@dimen/dimen_dp_13"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/ll_coupon_amount"
                        tools:text="满200减20" />

                    <TextView
                        android:id="@+id/tv_coupon_full_reduce_max"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/color_ff4244"
                        android:textSize="@dimen/dimen_dp_13"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tv_coupon_full_reduce"
                        tools:text="满200减20" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_to_use_coupon_bg_right"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:background="@drawable/shape_item_goods_coupon_right"
                    android:paddingStart="@dimen/dimen_dp_10"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/cl_to_use_coupon_bg_left"
                    app:layout_constraintTop_toTopOf="@+id/cl_to_use_coupon_bg_left">

                    <TextView
                        android:id="@+id/tv_coupon_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dimen_dp_8"
                        android:layout_marginEnd="@dimen/dimen_dp_10"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="@color/color_292933"
                        android:textSize="@dimen/dimen_dp_14"
                        android:textStyle="bold"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_goneMarginTop="@dimen/dimen_dp_9"
                        tools:text="店铺名" />

                    <TextView
                        android:id="@+id/tv_coupon_subtitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:maxLength="10"
                        android:textColor="@color/text_292933"
                        android:textSize="@dimen/dimen_dp_14"
                        app:layout_constraintStart_toStartOf="@+id/tv_coupon_title"
                        app:layout_constraintTop_toBottomOf="@+id/tv_coupon_title"
                        tools:text="优惠券名称 优惠券名称 优惠券名称" />


                    <TextView
                        android:id="@+id/tv_coupon_to_use_des"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="14.5dp"
                        android:layout_marginEnd="@dimen/dimen_dp_15"
                        android:ellipsize="end"
                        android:textColor="@color/color_676773"
                        android:textSize="@dimen/dimen_dp_12"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tv_coupon_subtitle"
                        tools:text="指定商品可用" />

                    <TextView
                        android:id="@+id/tv_coupon_to_use_date"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dimen_dp_10"
                        android:textColor="@color/color_676773"
                        android:textSize="@dimen/dimen_dp_12"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tv_coupon_to_use_des"
                        tools:text="2019/11/11-2019/12/11" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:layout_width="@dimen/dimen_dp_12"
                    android:layout_height="6dp"
                    android:layout_marginStart="@dimen/dimen_dp_94"
                    android:background="@drawable/shape_item_goods_coupon_top_half"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/cl_to_use_coupon_bg_left" />

                <View
                    android:layout_width="@dimen/dimen_dp_12"
                    android:layout_height="6dp"
                    android:layout_marginStart="@dimen/dimen_dp_94"
                    android:background="@drawable/shape_item_goods_coupon_bottom_half"
                    app:layout_constraintBottom_toBottomOf="@+id/cl_to_use_coupon_bg_left"
                    app:layout_constraintStart_toStartOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="45.5dp">

                <com.flyco.tablayout.CommonTabLayout
                    android:id="@+id/ctl_coupon_to_use"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:background="@drawable/base_header_default_bg"
                    android:paddingTop="6dp"
                    android:paddingBottom="6dp"
                    app:layout_constraintEnd_toStartOf="@+id/ib_to_user_coupon_category_all"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tl_indicator_bounce_enable="false"
                    app:tl_indicator_color="@color/home_back_selected"
                    app:tl_indicator_corner_radius="2dp"
                    app:tl_indicator_height="4dp"
                    app:tl_indicator_width="34dp"
                    app:tl_tab_space_equal="false"
                    app:tl_textBold="BOTH"
                    app:tl_textSelectColor="@color/text_292933"
                    app:tl_textSelectSize="15sp"
                    app:tl_textUnselectColor="@color/detail_tv_575766"
                    app:tl_textsize="15sp" />

                <ImageButton
                    android:id="@+id/ib_to_user_coupon_category_all"
                    android:layout_width="40dp"
                    android:layout_height="match_parent"
                    android:background="@drawable/base_header_default_bg"
                    android:src="@drawable/down_arrow"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:src="@drawable/icon_tablayout_masking"
                    app:layout_constraintEnd_toStartOf="@+id/ib_to_user_coupon_category_all" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dimen_dp_1"
                android:layout_marginTop="0.5dp"
                android:background="@color/color_F7F7F8" />
        </com.google.android.material.appbar.AppBarLayout>

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/vp_coupon_to_use"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            app:layout_scrollFlags="scroll|enterAlways" />

        <FrameLayout
            android:id="@+id/fl_to_use_coupon_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            app:layout_scrollFlags="scroll|enterAlways" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/dimen_dp_50"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/tv_coupon_to_use_amount_des"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/colors_fff7ef"
            android:paddingStart="@dimen/dimen_dp_10"
            android:paddingTop="@dimen/dimen_dp_7"
            android:paddingEnd="@dimen/dimen_dp_10"
            android:paddingBottom="@dimen/dimen_dp_7"
            android:textColor="@color/colors_99664D"
            android:textSize="@dimen/dimen_dp_14"
            app:layout_constraintBottom_toTopOf="@+id/tv_coupon_to_cart"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="再买¥65.00可使用优惠券" />

        <TextView
            android:id="@+id/tv_coupon_to_use_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dimen_dp_10"
            android:layout_marginTop="@dimen/dimen_dp_14"
            android:layout_marginBottom="@dimen/dimen_dp_14"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_16"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="小计：¥213.00" />

        <TextView
            android:id="@+id/tv_coupon_to_cart"
            android:layout_width="@dimen/dimen_dp_100"
            android:layout_height="@dimen/dimen_dp_50"
            android:background="@color/color_00B377"
            android:gravity="center"
            android:text="@string/go_to_cart"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>