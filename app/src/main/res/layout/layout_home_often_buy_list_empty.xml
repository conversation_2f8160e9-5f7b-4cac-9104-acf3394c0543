<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <ImageView
        android:layout_width="220dp"
        android:layout_height="145dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="143dp"
        android:src="@drawable/icon_often_buy_list_empty" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_5"
        android:gravity="center_horizontal"
        android:text="请在设置中开启您的定位服务 \n以获取所在地的商品数据"
        android:textColor="#ff999999"
        android:textSize="@dimen/dimen_dp_15" />

    <TextView
        android:id="@+id/tv_local"
        android:layout_width="@dimen/dimen_dp_150"
        android:layout_height="@dimen/dimen_dp_44"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dimen_dp_30"
        android:background="@drawable/shape_often_buy_list_empty_btn"
        android:gravity="center"
        android:text="去设置"
        android:textColor="@color/white" />

</LinearLayout>