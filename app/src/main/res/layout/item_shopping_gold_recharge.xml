<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="146dp"
    android:layout_height="86dp"
    android:layout_marginEnd="6dp">

    <com.ybmmarket20.common.widget.RoundConstraintLayout
        android:id="@+id/cl_recharge"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_80"
        android:layout_marginStart="3dp"
        android:layout_marginTop="3dp"
        app:layout_constraintDimensionRatio="109:85"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:rv_backgroundColor="@color/color_fff7e9"
        app:rv_cornerRadius="4dp">

        <TextView
            android:id="@+id/tv_money_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/color_591E00"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@id/cl_red_envelope"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="充10000元" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_red_envelope"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_money_content">

            <com.ybmmarket20.common.widget.RoundTextView
                android:id="@+id/tv_red_envelope"
                android:layout_width="wrap_content"
                android:layout_height="18dp"
                android:layout_marginStart="7dp"
                android:layout_marginTop="6dp"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1"
                android:paddingHorizontal="3dp"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:visibility="gone"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tv_can_use"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_goneMarginEnd="7dp"
                app:rv_backgroundColor="@color/color_FF223B"
                app:rv_cornerRadius_BL="2dp"
                app:rv_cornerRadius_TL="2dp"
                tools:text="返400元红包"
                tools:visibility="visible" />

            <com.ybmmarket20.common.widget.RoundTextView
                android:id="@+id/tv_can_use"
                android:layout_width="wrap_content"
                android:layout_height="18dp"
                android:layout_marginTop="6dp"
                android:layout_marginEnd="7dp"
                android:gravity="center"
                android:paddingHorizontal="2dp"
                android:text="本单可用"
                android:textColor="@color/color_FF223B"
                android:textSize="12sp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_red_envelope"
                app:layout_constraintTop_toTopOf="parent"
                app:rv_cornerRadius_BR="2dp"
                app:rv_cornerRadius_TR="2dp"
                app:rv_strokeColor="@color/color_FF223B"
                app:rv_strokeWidth="1dp"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/iv_shopping_gold_recharge"
            android:layout_width="14dp"
            android:layout_height="14dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="4dp"
            android:background="@drawable/selector_shopping_gold_recharge_checkbox"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </com.ybmmarket20.common.widget.RoundConstraintLayout>

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/tv_recommend"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:paddingStart="4dp"
        android:paddingTop="3dp"
        android:paddingEnd="4dp"
        android:paddingBottom="3dp"
        android:textColor="@color/color_FF223B"
        android:textSize="11sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:rv_backgroundColor="@color/colors_FFF3F3"
        app:rv_cornerRadius="3dp"
        tools:text="超值推荐"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>