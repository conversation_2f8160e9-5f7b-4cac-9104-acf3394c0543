<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"

    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    android:background="@color/white"
    android:gravity="center_vertical"
    android:orientation="vertical"
    android:paddingBottom="6dp"
    android:paddingLeft="10dp"
    android:paddingRight="10dp"
    android:paddingTop="6dp">

    <LinearLayout
        android:id="@+id/ll_root"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:orientation="horizontal">

        <com.ybmmarket20.common.widget.RoundedImageView
            android:id="@+id/iv_order"
            android:layout_width="86dp"
            android:layout_height="86dp"
            android:layout_gravity="center_vertical"
            android:padding="@dimen/product_image_padding"
            app:rv_cornerRadius="5dp"
            app:rv_strokeColor="#eeeeee"
            app:rv_strokeWidth="0.5dp" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="74dp"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="12dp"
            android:layout_weight="1"
            android:orientation="vertical"
            android:paddingTop="1dp">

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:text=""
                android:textColor="@color/text_292933"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_price"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:includeFontPadding="false"
                android:singleLine="true"
                android:text=""
                android:textColor="@color/text_9494A6"
                android:textSize="14sp" />

            <com.ybmmarket20.view.TagView
                android:id="@+id/tg"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />


        </LinearLayout>

        <TextView
            android:id="@+id/tv_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:includeFontPadding="false"
            android:text=""
            android:textColor="@color/text_9494A6"
            android:textSize="14sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_price_detail"
        android:layout_width="match_parent"
        android:layout_height="128dp"
        android:layout_marginTop="4dp"
        android:background="@drawable/gray_corners_bg_2"
        android:orientation="vertical"
        android:paddingLeft="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                style="@style/order_price_ll"
                android:layout_weight="1.2">

                <TextView
                    android:id="@+id/tv_rel_price"
                    style="@style/order_product_price_text_style"
                    android:text="原单价" />

                <TextView
                    style="@style/order_price_optioin"
                    android:paddingLeft="10dp"
                    android:text="—" />
            </LinearLayout>

            <LinearLayout style="@style/order_price_ll">

                <TextView
                    android:id="@+id/tv_order_coupon"
                    style="@style/order_product_price_text_style"
                    android:text="优惠" />

                <TextView
                    style="@style/order_price_optioin"
                    android:text="—" />
            </LinearLayout>

            <LinearLayout
                style="@style/order_price_ll"
                android:layout_weight="1.2">

                <TextView
                    android:id="@+id/tv_rebate"
                    style="@style/order_product_price_text_style"
                    android:text="余额抵扣" />

                <TextView
                    style="@style/order_price_optioin"
                    android:text="≈" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_rk"
                style="@style/order_product_price_text_style"
                android:text="实付价"
                android:textStyle="bold"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_rel_price_value"
                style="@style/order_product_price_text_style"
                android:layout_weight="1.2" />

            <TextView
                android:id="@+id/tv_order_coupon_value"
                style="@style/order_product_price_text_style" />

            <TextView
                android:id="@+id/tv_rebate_value"
                style="@style/order_product_price_text_style"
                android:layout_weight="1.2" />

            <TextView
                android:id="@+id/tv_rk_value"
                style="@style/order_product_price_text_style"
                android:textColor="#FFFF0000"
                android:textStyle="bold"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                style="@style/order_price_ll"
                android:layout_weight="1.2">

                <TextView
                    android:id="@+id/tv_rel_price_tow"
                    style="@style/order_product_price_text_style"
                    android:text="原单价" />

                <TextView
                    style="@style/order_price_optioin"
                    android:paddingLeft="10dp"
                    android:text="—" />
            </LinearLayout>

            <LinearLayout style="@style/order_price_ll">

                <TextView
                    android:id="@+id/tv_order_coupon_tow"
                    style="@style/order_product_price_text_style"
                    android:text="优惠" />

                <TextView
                    style="@style/order_price_optioin"
                    android:text="—" />
            </LinearLayout>

            <LinearLayout
                style="@style/order_price_ll"
                android:layout_weight="1.2">

                <TextView
                    android:id="@+id/tv_rebate_tow"
                    style="@style/order_product_price_text_style"
                    android:text="返利" />

                <TextView
                    style="@style/order_price_optioin"
                    android:text="≈" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_rk_tow"
                style="@style/order_product_price_text_style"
                android:text="成本价"
                android:textStyle="bold"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_rel_price_value_tow"
                style="@style/order_product_price_text_style"
                android:layout_weight="1.2" />

            <TextView
                android:id="@+id/tv_order_coupon_value_tow"
                style="@style/order_product_price_text_style" />

            <TextView
                android:id="@+id/tv_rebate_value_tow"
                style="@style/order_product_price_text_style"
                android:layout_weight="1.2" />

            <TextView
                android:id="@+id/tv_rk_value_tow"
                style="@style/order_product_price_text_style"
                android:textColor="#FFFF0000"
                android:textStyle="bold"/>
        </LinearLayout>

    </LinearLayout>
</LinearLayout>
