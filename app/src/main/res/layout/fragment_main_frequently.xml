<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white">

    <View
        android:id="@+id/status_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:background="@color/white"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="match_parent"
        android:layout_height="@dimen/header_height"
        android:layout_gravity="center"
        android:gravity="center"
        android:text="@string/main_tab_frequently"
        android:textColor="@color/pabr_333333"
        android:textSize="@dimen/pax_core_sp_17"
        app:layout_constraintTop_toBottomOf="@id/status_bar" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/color_f6f6f6"
        app:layout_constraintTop_toBottomOf="@id/tvTitle"/>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/smartrefresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:srlEnableLoadMore="false"
        android:layout_marginTop="1dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTitle">

        <com.ybmmarket20.view.HomeSteadyHeader
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rlvFrequently"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="@dimen/dimen_dp_10"
            android:layout_marginRight="@dimen/dimen_dp_10"
            android:orientation="vertical"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>
</androidx.constraintlayout.widget.ConstraintLayout>