package com.ybm.app.bean;

import android.Manifest;
import android.annotation.TargetApi;
import android.app.ActivityManager;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;

import androidx.core.app.ActivityCompat;

import android.telephony.TelephonyManager;

import com.apkfuns.logutils.LogUtils;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.utils.MemoryManager;
import com.ybm.app.utils.RomUtils;
import com.ybm.app.utils.UiUtils;

import java.io.File;
import java.io.FileFilter;
import java.io.FileInputStream;
import java.io.IOException;

public class DeviceEntity {

    public String code = "";//imei
    public int api;
    public String device = "";
    public String model = "";
    public String product = "";
    public String os = "";
    public String cpu_abi = "";
    public String cpu_abi2 = "";
    public int cpu_core;//cpu 核心数
    public long sysMemory;//内存
    public long sysfreeMemory;//内存
    public long appMemory;//内存
    public long diskMemory;//磁盘总内存
    public long diskFreeMemory;//磁盘没有使用
    public long appfreeMemory;//内存
    public long appMaxMemory;//内存
    public int cpu_hz;//cpu 主频
    public int width;//
    public int heigth;//

    public DeviceEntity(Context context) {
        initData(context);
    }

    public void initData(Context context) {
        api = Build.VERSION.SDK_INT;
        device = Build.BRAND;
        model = Build.MODEL;
        product = Build.PRODUCT;
        cpu_abi = Build.CPU_ABI;
        cpu_abi2 = Build.CPU_ABI2;
        os = RomUtils.getRomOs();
        sysMemory = getTotalMemory(context);
        sysfreeMemory = getSysfreeMemory(context);
        appfreeMemory = getAppfreeMemory();
        appMemory = getAppMemory();
        appMaxMemory = getAppMaxMemory();
        cpu_core = getNumberOfCPUCores();
        cpu_hz = getCPUMaxFreqKHz();
//        code = getDeviceId(context); // 默认不生成code，等待用户确认完隐私协议，再生成code
        width = UiUtils.getScreenWidth();
        heigth = UiUtils.getScreenHeight();
        diskMemory = MemoryManager.getTotalInternalMemorySize();
        diskFreeMemory = MemoryManager.getAvailableInternalMemorySize();
    }

    //获取设备唯一标识
//    public String getDeviceId(Context context) {
//        String id = "";
//        try {
//            TelephonyManager telManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
//            if (ActivityCompat.checkSelfPermission(context, Manifest.permission.READ_PHONE_STATE) == PackageManager.PERMISSION_GRANTED) {
//                id = telManager.getDeviceId();
//            }
//        } catch (Exception e) {
//            BugUtil.sendBug(e);
//            id = "";
//        }
//        return id;
//    }

    public int getNumberOfCPUCores() {
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.GINGERBREAD_MR1) {
            return 1;
        }
        int cores;
        try {
            cores = new File("/sys/devices/system/cpu/").listFiles(getCPUFilter()).length;
        } catch (SecurityException e) {
            cores = -1;
        } catch (NullPointerException e) {
            cores = -1;
        }
        return cores;
    }

    public FileFilter getCPUFilter() {
        return new FileFilter() {
            @Override
            public boolean accept(File pathname) {
                String path = pathname.getName();
                //regex is slow, so checking char by char.
                if (path.startsWith("cpu")) {
                    for (int i = 3; i < path.length(); i++) {
                        if (path.charAt(i) < '0' || path.charAt(i) > '9') {
                            return false;
                        }
                    }
                    return true;
                }
                return false;
            }
        };
    }

    @TargetApi(Build.VERSION_CODES.JELLY_BEAN)
    public long getTotalMemory(Context c) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            ActivityManager.MemoryInfo memInfo = new ActivityManager.MemoryInfo();
            ActivityManager am = (ActivityManager) c.getSystemService(Context.ACTIVITY_SERVICE);
            am.getMemoryInfo(memInfo);
            if (memInfo != null) {
                return memInfo.totalMem;
            } else {
                return -1;
            }
        } else {
            long totalMem = 0;
            try {
                FileInputStream stream = new FileInputStream("/proc/meminfo");
                try {
                    totalMem = parseFileForValue("MemTotal", stream);
                    totalMem *= 1024;
                } finally {
                    stream.close();
                }
            } catch (IOException e) {
            }
            return totalMem;
        }
    }

    @TargetApi(Build.VERSION_CODES.JELLY_BEAN)
    public long getSysfreeMemory(Context c) {
        ActivityManager.MemoryInfo memInfo = new ActivityManager.MemoryInfo();
        ActivityManager am = (ActivityManager) c.getSystemService(Context.ACTIVITY_SERVICE);
        am.getMemoryInfo(memInfo);
        if (memInfo != null) {
            return memInfo.availMem;
        } else {
            return -1;
        }
    }

    public long getAppMemory() {
        return Runtime.getRuntime().totalMemory();
    }

    public long getAppfreeMemory() {
        return Runtime.getRuntime().freeMemory();
    }

    public long getAppMaxMemory() {
        return Runtime.getRuntime().maxMemory();
    }

    public int getCPUMaxFreqKHz() {
        int maxFreq = -1;
        try {
            for (int i = 0; i < getNumberOfCPUCores(); i++) {
                String filename =
                        "/sys/devices/system/cpu/cpu" + i + "/cpufreq/cpuinfo_max_freq";
                File cpuInfoMaxFreqFile = new File(filename);
                if (cpuInfoMaxFreqFile.exists()) {
                    byte[] buffer = new byte[128];
                    FileInputStream stream = new FileInputStream(cpuInfoMaxFreqFile);
                    try {
                        stream.read(buffer);
                        int endIndex = 0;
                        //Trim the first number out of the byte buffer.
                        while (buffer[endIndex] >= '0' && buffer[endIndex] <= '9'
                                && endIndex < buffer.length) endIndex++;
                        String str = new String(buffer, 0, endIndex);
                        Integer freqBound = Integer.parseInt(str);
                        if (freqBound > maxFreq) maxFreq = freqBound;
                    } catch (NumberFormatException e) {
                        //Fall through and use /proc/cpuinfo.
                    } finally {
                        stream.close();
                    }
                }
            }
            if (maxFreq == -1) {
                FileInputStream stream = new FileInputStream("/proc/cpuinfo");
                try {
                    int freqBound = parseFileForValue("cpu MHz", stream);
                    freqBound *= 1000; //MHz -> kHz
                    if (freqBound > maxFreq) maxFreq = freqBound;
                } finally {
                    stream.close();
                }
            }
        } catch (IOException e) {
            maxFreq = -1; //Fall through and return unknown.
        }
        return maxFreq;
    }

    private int parseFileForValue(String textToMatch, FileInputStream stream) {
        byte[] buffer = new byte[1024];
        try {
            int length = stream.read(buffer);
            for (int i = 0; i < length; i++) {
                if (buffer[i] == '\n' || i == 0) {
                    if (buffer[i] == '\n') i++;
                    for (int j = i; j < length; j++) {
                        int textIndex = j - i;
                        //Text doesn't match query at some point.
                        if (buffer[j] != textToMatch.charAt(textIndex)) {
                            break;
                        }
                        //Text matches query here.
                        if (textIndex == textToMatch.length() - 1) {
                            return extractValue(buffer, j);
                        }
                    }
                }
            }
        } catch (IOException e) {
            //Ignore any exceptions and fall through to return unknown value.
        } catch (NumberFormatException e) {
        }
        return -1;
    }

    private static int extractValue(byte[] buffer, int index) {
        while (index < buffer.length && buffer[index] != '\n') {
            if (buffer[index] >= '0' && buffer[index] <= '9') {
                int start = index;
                index++;
                while (index < buffer.length && buffer[index] >= '0' && buffer[index] <= '9') {
                    index++;
                }
                String str = new String(buffer, 0, start, index - start);
                return Integer.parseInt(str);
            }
            index++;
        }
        return -1;
    }
}
