package com.ybm.app.view.refresh;

import android.content.Context;
import android.content.res.XmlResourceParser;
import android.util.Log;
import android.widget.ImageView;

import com.ybm.app.R;

import org.xmlpull.v1.XmlPullParser;

import java.util.ArrayList;
import java.util.List;

public class AnimationMan {

    private ImageView imageView;

    //是否循环播放
    private boolean oneShot = true;

    private Context context;

    private List<FrameInfo> frameInfoList;

    private static final int SIZE = 3;
    private int frame;


    public AnimationMan(ImageView imageView, int resourceId) {
        this.imageView = imageView;
        this.context = imageView.getContext();
        frameInfoList = getFramesFromXml(context, resourceId);

        for (int i = 0; i < frameInfoList.size(); i++) {
            if (i <= SIZE) {
                FrameInfo frameInfo = frameInfoList.get(i);
                frameInfo.createBitmap(context);
            }
        }

    }


    public void setOneShot(boolean oneShot) {
        this.oneShot = oneShot;
    }


    private int position;


    public void start() {
        if (listener != null) {
            listener.onStart();
        }
        stop=false;
        stopNatural=false;
        showFrameAtPosition();

    }

    private FrameInfo oldFrameInfo;

    private boolean stop;


    private boolean stopNatural;

    public FrameInfo currentFrame;

    public void showFrameAtPosition() {

        if (stop) {
            if (listener != null) {
                listener.onStop();
            }
            return;
        }
        if (position == frameInfoList.size()) {
            position = 0;
            if (stopNatural) {
                if (listener != null) {
                    listener.onStop();

                }
                return;
            }
            if (!oneShot) {
                if (listener != null) {
                    listener.onStop();
                }
                return;
            }
            if (listener != null) {
                listener.onRepeat();
            }
        }

        currentFrame = frameInfoList.get(position);

        imageView.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (currentFrame.bitmap != null&&!currentFrame.bitmap.isRecycled()) {
                    imageView.setImageBitmap(currentFrame.bitmap);
                }
                if (oldFrameInfo != null&&oldFrameInfo!=currentFrame) {
                    oldFrameInfo.releaseBitmap();
                }
                oldFrameInfo = currentFrame;
                frameInfoList.get((position + SIZE) % frameInfoList.size()).createBitmap(context);
                position++;
                showFrameAtPosition();
            }
        }, currentFrame.duration);

    }

    /**
     * 结束当前动画在任意帧
     */
    public void stopNow() {
        stop = true;
        releaseAll();
    }

    /**
     * 自然结束 完成整个帧动画后结束，而并非中途停止
     */
    public void stopNatural() {
        stopNatural = true;
        releaseAll();
    }

    public void releaseAll() {
        for (FrameInfo frameInfo : frameInfoList) {
            if (frameInfo != currentFrame) {
                frameInfo.releaseBitmap();
//                Log.i("回收剩余", frameInfo.toString());
            }

        }
    }


    private Listener listener;

    public void setFrame(int position) {
        FrameInfo frameInfo = getFrameInfo(position);
        if (!frameInfo.bitmap.isRecycled()){
            imageView.setImageBitmap(frameInfo.bitmap);
        }
        if (currentFrame != frameInfo) {
            currentFrame.releaseBitmap();
            currentFrame = frameInfo;
        }
    }

    public interface Listener {

        void onStart();

        void onStop();

        void onRepeat();

    }


    public List<FrameInfo> getFramesFromXml(Context context, int id) {
        ArrayList<FrameInfo> frameInfoList = null;

        XmlResourceParser parser = context.getResources().getXml(id);

        try {
            while (parser.getEventType() != XmlPullParser.END_DOCUMENT) {

                if (parser.getEventType() == XmlPullParser.START_TAG) {
                    if ("animation-list".equals(parser.getName())) {
                        frameInfoList = new ArrayList<>();
                        boolean attributeValue = parser.getAttributeBooleanValue("http://schemas.android.com/apk/res/android", "oneshot", false);

                    } else if ("item".equals(parser.getName())) {
                        FrameInfo frameInfo = new FrameInfo();
                        frameInfo.duration = parser.getAttributeIntValue("http://schemas.android.com/apk/res/android", "duration", 0);
                        frameInfo.resourceId = parser.getAttributeResourceValue("http://schemas.android.com/apk/res/android", "drawable", 0);
                        frameInfo.resourceIdStr = parser.getAttributeValue("http://schemas.android.com/apk/res/android", "drawable");
                        frameInfoList.add(frameInfo);
                    }
                }

                parser.next();

            }

            return frameInfoList;

        } catch (Exception e) {
            e.printStackTrace();

        }
        return null;


    }

    public void setListener(Listener listener) {

        this.listener = listener;
    }


    public FrameInfo getFrameInfo(int position) {
        int framePosition = position;
        if(frameInfoList==null) frameInfoList = new ArrayList<>();
        if(frameInfoList.isEmpty()){
            FrameInfo frameInfo = new FrameInfo();
            frameInfo.duration = 0;
            frameInfo.resourceId = R.drawable.base_refresh_loading;
            frameInfo.resourceIdStr = "";
            frameInfoList.add(frameInfo);
        }
        //防止越界
        if(framePosition < 0) framePosition = 0;
        else if(framePosition > frameInfoList.size()-1) framePosition = frameInfoList.size()-1;
        FrameInfo frameInfo = frameInfoList.get(framePosition);
        if (frameInfo.bitmap == null) {
            frameInfo.createBitmap(context);
        }
        currentFrame = frameInfo;
        return frameInfo;
    }

    public int getDuration() {

        int count = 0;
        for (FrameInfo frameInfo : frameInfoList) {
            count += frameInfo.duration;
        }
        return count;


    }

    public int getSize() {

        return frameInfoList.size();

    }

}
