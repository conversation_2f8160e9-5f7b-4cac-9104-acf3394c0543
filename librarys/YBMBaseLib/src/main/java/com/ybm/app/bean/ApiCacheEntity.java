package com.ybm.app.bean;


import android.text.TextUtils;

/**
 * 接口缓存实体类
 */
public class ApiCacheEntity {
	public final static String FLAG_START = "Ｓ";
	public final static String FLAG_MID = "Ｍ";
	public final static String FLAG_END = "Ｅ";
	public final static String FLAG_SPLIT = "Ｌ";
	public long lastCacheTime = 0;
	public long maxCacheTime = 0; //最大缓存失效时间 单位 分钟; 如果给0 就是系统默认时长为1天
	public int isHeigthCache = 0; //1 高速缓存  0 本地缓存 默认值
	public String cache = "";
	public ApiCacheEntity() {

	}

	/**
	 * @param isHeigthCache 内存缓存  给1内存缓存  0本地缓存
	 */
	public void  set(int isHeigthCache) {
		this.isHeigthCache = isHeigthCache;
		this.lastCacheTime = System.currentTimeMillis();
	}
	
	/**
	 * @param maxCacheTime 最大失效时间  单位分钟
	 */
	public void set(long maxCacheTime) {
		set(maxCacheTime,0);
	}

	public void set(long maxCacheTime, int isHeigthCache) {
		this.maxCacheTime = maxCacheTime;
		this.isHeigthCache = isHeigthCache;
		this.lastCacheTime = System.currentTimeMillis();
	}
	/**
	 * @return 缓存数据是否已经失效 true 已经失效
	 */
	public boolean isCacheTimeOut(){
		if(this.maxCacheTime <=0){
			return true;
		}
		return System.currentTimeMillis()-this.maxCacheTime>this.lastCacheTime;
	}

	@Override
	public String toString() {
		StringBuffer temp =new StringBuffer();
		temp.append(FLAG_START);
		if(cache !=null){
			temp.append(cache);
		}
		temp.append(string2Me());
		return temp.toString();
	}

	public String string2Me(){
		StringBuffer temp =new StringBuffer();
		temp.append(FLAG_MID).append(maxCacheTime).append(FLAG_SPLIT).append(isHeigthCache).append(FLAG_SPLIT).append(lastCacheTime).append(FLAG_END);
		return temp.toString();
	}

	public static ApiCacheEntity  parseString(String str){
		if(TextUtils.isEmpty(str) || !str.startsWith(FLAG_START) || !str.endsWith(FLAG_END) ||!str.contains(FLAG_MID) || !str.contains(FLAG_SPLIT)){
			return null;
		}
		ApiCacheEntity entity = new ApiCacheEntity();
		int indexStart = str.lastIndexOf(FLAG_MID);
		entity.cache = str.substring(1,indexStart);
		String temp = str.substring(indexStart+1,str.lastIndexOf(FLAG_END));
		if(temp !=null && temp.contains(FLAG_SPLIT));
		String[] params = temp.split(FLAG_SPLIT);
		if(params !=null && params.length == 3){
			try {
				entity.lastCacheTime = Long.parseLong(params[2]);
			}catch (Exception e){

			}
			try {
				entity.isHeigthCache = Integer.parseInt(params[1]);
			}catch (Exception e){

			}
			try {
				entity.maxCacheTime = Long.parseLong(params[0]);
			}catch (Exception e){

			}
		}
		return entity;
	}

	public static long  getLastTime(String str){
		if(TextUtils.isEmpty(str) || !str.startsWith(FLAG_START) || !str.endsWith(FLAG_END) ||!str.contains(FLAG_MID) || !str.contains(FLAG_SPLIT)){
			return 0;
		}
		String time = str.substring(str.lastIndexOf(FLAG_SPLIT)+1,str.lastIndexOf(FLAG_END));
		try {
			return Long.parseLong(time);
		}catch (Exception e){

		}
		return 0;
	}

}
