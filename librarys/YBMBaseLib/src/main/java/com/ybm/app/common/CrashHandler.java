package com.ybm.app.common;

import android.util.Log;

/**
 * 异常处理类
 */
public class Crash<PERSON><PERSON>ler implements Thread.UncaughtExceptionHandler {

    private Thread.UncaughtExceptionHandler defaultHandler;
    private static CrashHandler mInstance;

    public static CrashHandler getInstance(){
        if (mInstance == null) {
            synchronized (CrashHandler.class) {
                if (mInstance == null) {
                    mInstance = new CrashHandler();
                }
            }
        }
        return mInstance;
    }

    private CrashHandler() {

    }

    public void regestCrash(){
        defaultHandler = Thread.getDefaultUncaughtExceptionHandler();
        Thread.setDefaultUncaughtExceptionHandler(this);
    }

    public  void uncaughtException(Thread thread,Throwable throwable) {
        Log.d("YBM-","uncaughtException");
        UpdateManager.clearPatch(throwable);
        if (defaultHandler != null) {
            defaultHandler.uncaughtException(thread, throwable);
        }else {
            android.os.Process.killProcess(android.os.Process.myPid());
        }
    }

}
