package com.ybmmarket20.xyyreport.page

import com.xyy.xyyreport.IReport
import com.xyy.xyyreport.ReportName
import com.xyy.xyyreport.ReportParam

/**
 * <AUTHOR>
 * @date  2025/1/7
 * @description
 */

//页面曝光
@ReportName("page_exposure")
class PageExposureBean {

}

//页面曝光
@ReportName("page_exposure")
class PageSearchExposureBean {
    @ReportParam("key_word")
    val keyWord: String? = null
}

//组件曝光
@ReportName("page_component_exposure")
class PageComponentExposureBean {

}

//子模块曝光
@ReportName("page_sub_module_exposure")
class PageSubModuleExposureBean {

}

//子模块点击
@ReportName("action_sub_module_click")
class ActionSubModuleClickBean {

}

//子模块点击-加价购or组合购
@ReportName("action_sub_module_click")
class ActionSubModuleGroupCombinationClickBean {
    @ReportParam("qt_list_data")
    val qtListData: String? = null
    @ReportParam("qt_sku_data")
    val qtSkuData: String? = null
}

//商品子模块点击
@ReportName("action_list_product_click")
class ActionSubModuleGoodsClickBean {
    @ReportParam("product_id")
    val productId: Long = 0
    @ReportParam("product_name")
    val productName: String? = null
}

//搜索商品子模块点击
@ReportName("action_list_product_click")
class ActionSubModuleSearchGoodsClickBean {
    @ReportParam("product_id")
    val productId: Long = 0
    @ReportParam("product_name")
    val productName: String? = null
    @ReportParam("qt_list_data")
    val qtListData: String? = null
    @ReportParam("qt_sku_data")
    val qtSkuData: String? = null
}

//商品子模块曝光
@ReportName("page_list_product_exposure")
class PageSubModuleGoodsExposureBean {
    @ReportParam("product_id")
    val productId: Long = 0
    @ReportParam("product_name")
    val productName: String? = null

}

//搜索商品子模块曝光
@ReportName("page_list_product_exposure")
class PageSubModuleSearchGoodsExposureBean {
    @ReportParam("product_id")
    val productId: Long = 0
    @ReportParam("product_name")
    val productName: String? = null
    @ReportParam("qt_list_data")
    val qtListData: String? = null
    @ReportParam("qt_sku_data")
    val qtSkuData: String? = null
}

//商品按钮点击
@ReportName("action_product_button_click")
class ActionProductButtonClickBean {
    @ReportParam("product_id")
    val productId: Long = 0
    @ReportParam("product_name")
    val productName: String? = null
}

//搜索商品按钮点击
@ReportName("action_product_button_click")
class ActionSearchProductButtonClickBean {
    @ReportParam("product_id")
    val productId: Long = 0
    @ReportParam("product_name")
    val productName: String? = null
    @ReportParam("qt_list_data")
    val qtListData: String? = null
    @ReportParam("qt_sku_data")
    val qtSkuData: String? = null
}

//按钮点击
@ReportName("action_button_click")
class ActionButtonClickBean {

}

//列表生成
class ReportListCreateBean: IReport {

    var qtListData: String? = null
    var sort: String? = null
    var isFilter: Int? = 0
    var manufacturers: Array<String>? = null
    var distributors: Array<String>? = null
    var specs: Array<String>? = null
    var drugClassifications: Array<String>? = null
    var periodValidity: String? = null
    var categorys: Array<String>? = null
    var minPrice: Double? = null
    var maxPrice: Double? = null
    var dynamicFilterNames: Array<String>? = null
    var dynamicFilterTypes: Array<String>? = null

    override fun getReportName(): String = "page_list_build"

    override fun getReportParamsMap(): Map<String, Any?> {
        val map = mutableMapOf<String, Any?>(
            "qt_list_data" to qtListData,
            "sort" to sort,
            "is_filter" to isFilter,
            "manufacturers" to manufacturers,
            "distributors" to distributors,
            "specs" to specs,
            "drug_classifications" to drugClassifications,
            "period_validity" to periodValidity,
            "categorys" to categorys,
            "min_price" to minPrice,
            "max_price" to maxPrice,
            "dynamic_filter_names" to dynamicFilterNames,
            "dynamic_filter_types" to dynamicFilterTypes
        )
        val iterator = map.iterator()
        while (iterator.hasNext()) {
            if (iterator.next().value == null) {
                iterator.remove()
            }
        }
        return map
    }

}