package com.ybmmarket20.xyyreport.page.orderList

import android.content.Context
import com.ybmmarket20.report.ReportActionSubModuleClickBean
import com.ybmmarket20.report.ReportPageExposureBean
import com.ybmmarket20.report.ReportPageSubModuleExposureBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.page.order.OrderConstant
import com.ybmmarket20.xyyreport.paramsInfo.IOrderListItem
import com.ybmmarket20.xyyreport.spm.ScmBean
import com.ybmmarket20.xyyreport.spm.SpmUtil

object OrderListReport {

    @JvmStatic
    fun pvOrderList(context: Context, tabType: String?) {
        val spm = SpmUtil.getSpmPv("orderList_0-0_$tabType")
        SpmLogUtil.print("订单列表-PV")
        ReportUtil.pvTrack(context, ReportPageExposureBean(), spm)
        SpmUtil.checkAnalysisContext(context) {
            it.putExtension(OrderConstant.ORDER_ACTION_PAGE_IS_LIST, true)
        }
    }

    @JvmStatic
    fun trackSearchClick(context: Context) {
        SpmUtil.checkAnalysisContext(context) {
            val spm = it.getSpmCtn()?.newInstance()?.apply {
                spmC = "orderListHeader@1"
                spmD = "searchBox@1"
            }
            val scm = ScmBean().apply {
                scmA = "appFE"
                scmB = "0"
                scmC = "all_0"
                scmD = "0"
            }
            SpmLogUtil.print("订单列表-顶部搜索框点击")
            ReportUtil.clickTrack(context, ReportActionSubModuleClickBean(), spm, scm)
        }
    }

    @JvmStatic
    fun trackOrderItemShopTextBtnClick(context: Context, iOrderListItem: IOrderListItem?, position: Int) {
        SpmUtil.checkAnalysisContext(context) {
            val spm = it.getSpmCtn()?.newInstance()?.apply {
                spmC = "orderList@2"
                spmD = "orderCard@${position + 1}_title@1"
            }
            val scm = ScmBean().apply {
                scmA = "order"
                scmB = "0"
                scmC = "all_0"
                scmD = "order-${iOrderListItem?.getOrderNo()}_orderStatus-${OrderListReportUtil.getOrderStatusText(iOrderListItem?.getOrderStatus())}_text-${SpmUtil.checkReportSpmFormat(iOrderListItem?.getOrderShopName())}"
            }
            SpmLogUtil.print("订单列表-店铺名称点击")
            ReportUtil.clickTrack(context, ReportActionSubModuleClickBean(), spm, scm)
        }
    }

    @JvmStatic
    fun trackOrderItemBuyAgainBtnClick(context: Context, iOrderListItem: IOrderListItem?, position: Int) {
        SpmUtil.checkAnalysisContext(context) {
            val isListAny = it.getExtensionValue(OrderConstant.ORDER_ACTION_PAGE_IS_LIST)
            if (isListAny == null || isListAny !is Boolean) return@checkAnalysisContext
            val isList = it.getExtensionValue(OrderConstant.ORDER_ACTION_PAGE_IS_LIST) as Boolean
            if (isList) {
                trackOrderItemListBuyAgainBtnClick(context, iOrderListItem, position)
            } else {
                trackOrderItemDetailBuyAgainBtnClick(context)
            }
        }

    }

    private fun trackOrderItemListBuyAgainBtnClick(context: Context, iOrderListItem: IOrderListItem?, position: Int) {
        SpmUtil.checkAnalysisContext(context) {
            val spm = it.getSpmCtn()?.newInstance()?.apply {
                spmC = "orderList@2"
                spmD = "orderCard@${position + 1}_btn@1"
            }
            val scm = ScmBean().apply {
                scmA = "order"
                scmB = "0"
                scmC = "all_0"
                scmD = "order-${iOrderListItem?.getOrderNo()}_orderStatus-${OrderListReportUtil.getOrderStatusText(iOrderListItem?.getOrderStatus())}_text-再次购买"
            }
            SpmLogUtil.print("订单列表-再次购买点击")
            SpmUtil.setScmE(scm)
            it.putExtension(OrderConstant.ORDER_ACTION_BUY_AGAIN_SPM_CNT, spm)
            it.putExtension(OrderConstant.ORDER_ACTION_BUY_AGAIN_SCM_CNT, scm)
            ReportUtil.track(context, ReportActionSubModuleClickBean(), spm, scm)
        }
    }

    @JvmStatic
    fun trackOrderItemDetailBuyAgainBtnClick(context: Context) {
        SpmUtil.checkAnalysisContext(context) {
            val spm = it.getSpmCtn()?.newInstance()?.apply {
                spmC = "ftorderDetail@Z"
                spmD = "btn@2"
            }
            val scm = ScmBean().apply {
                scmA = "order"
                scmB = "0"
                scmC = "all_0"
                scmD = "text-再次购买"
            }
            SpmLogUtil.print("订单详情-再次购买点击")
            SpmUtil.setScmE(scm)
            it.putExtension(OrderConstant.ORDER_ACTION_BUY_AGAIN_SPM_CNT, spm)
            it.putExtension(OrderConstant.ORDER_ACTION_BUY_AGAIN_SCM_CNT, scm)
            ReportUtil.track(context, ReportActionSubModuleClickBean(), spm, scm)
        }
    }

    private fun onOrderListSubModuleExposure(context: Context, btnType: String?, cmsGroupId: String?, btnText: String?, contentId: String?) {
        SpmUtil.checkAnalysisContext(context) {
            val cgId = if (cmsGroupId.isNullOrEmpty() || cmsGroupId == "0") "all_0" else "${cmsGroupId}_0"
            val spm = it.getSpmCtn()?.newInstance()?.apply {
                spmC = "orderListHeader@1"
                spmD = "$btnType"
            }
            val scm = ScmBean().apply {
                scmA = "cms"
                scmB = "0"
                scmC = cgId
                scmD = "cpn-${contentId}_text-$btnText"
            }
            SpmUtil.setScmE(scm)
            ReportUtil.track(context, ReportPageSubModuleExposureBean(), spm, scm)
        }
    }

    /**
     * 订单列表优惠券曝光
     */
    fun onOrderListCouponExposure(context: Context, cmsGroupId: String?, btnText: String?, contentId: String?) {
        SpmLogUtil.print("订单列表-顶部优惠券曝光")
        onOrderListSubModuleExposure(context, "subTitle@3_btn@1", cmsGroupId, btnText, contentId)
    }

    private fun onOrderListSubModuleClick(context: Context, btnType: String?, cmsGroupId: String?, btnText: String?, contentId: String?) {
        SpmUtil.checkAnalysisContext(context) {
            val cgId = if (cmsGroupId.isNullOrEmpty() || cmsGroupId == "0") "all_0" else "${cmsGroupId}_0"
            val spm = it.getSpmCtn()?.newInstance()?.apply {
                spmC = "orderListHeader@1"
                spmD = "$btnType"
            }
            val scm = ScmBean().apply {
                scmA = "cms"
                scmB = "0"
                scmC = cgId
                scmD = "cpn-${contentId}_text-$btnText"
            }
            SpmUtil.setScmE(scm)
            ReportUtil.track(context, ReportActionSubModuleClickBean(), spm, scm)
        }
    }

    /**
     * 订单列表优惠券点击
     */
    fun onOrderListCouponClick(context: Context, cmsGroupId: String?, btnText: String?, contentId: String?) {
        SpmLogUtil.print("订单列表-顶部优惠券点击")
        onOrderListSubModuleClick(context, "subTitle@3_btn@1", cmsGroupId, btnText, contentId)
    }


}