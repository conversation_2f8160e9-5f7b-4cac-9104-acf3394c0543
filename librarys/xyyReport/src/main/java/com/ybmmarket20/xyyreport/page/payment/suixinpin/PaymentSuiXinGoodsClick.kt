package com.ybmmarket20.xyyreport.page.payment.suixinpin

import android.content.Context
import com.ybmmarket20.report.ReportActionSearchProductButtonClickBean
import com.ybmmarket20.report.ReportActionSubModuleSearchGoodsClickBean
import com.ybmmarket20.report.ReportPageSubModuleSearchGoodsExposureBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.page.payment.IPaymentSuiXinPinGoods
import com.ybmmarket20.xyyreport.session.SessionManager
import com.ybmmarket20.xyyreport.spm.ScmBean
import com.ybmmarket20.xyyreport.spm.SpmUtil

sealed class PaymentSuiXinGoodsClick(
    private val context: Context,
    val goodsInfo: IPaymentSuiXinPinGoods
) {

    companion object {
        //加购
        const val REPORT_SUIXINPIN_BUTTON_CLICK_ADD_CART = 1

        //加
        const val REPORT_SUIXINPIN_BUTTON_CLICK_ADD = 3

        //加购数量
        const val REPORT_SUIXINPIN_BUTTON_CLICK_ADD_NUMBER = 2
    }
    private var clickType: Int = 0
    private var clickText: String = ""

    open fun getExposureSpmC(): String? = null
    open fun getExposureSpmD(): String? = null
    private fun getExposureScmA(): String = "recommend"
    private fun getExposureScmB(): String = if (goodsInfo.getExpId().isNullOrEmpty()) "0" else goodsInfo.getExpId()!!
    private fun getExposureScmC(): String = "all_0"
    private fun getExposureScmD(): String = "prod-${goodsInfo.getProductSkuSkuId()}"
    open fun logMessage(): String = ""

    private fun trackClickGoods(): String? {
        return SpmUtil.checkAnalysisContextReturn(context) {
            val spScmE = "${goodsInfo.getScmId()}${SessionManager.get().newGoodsScmRandom()}"
            val spm = it.getSpmCtnNewInstance()?.apply {
                spmC = getExposureSpmC()
                spmD = getExposureSpmD()
            }
            val scm = ScmBean().apply {
                scmA = getExposureScmA()
                scmB = getExposureScmB()
                scmC = getExposureScmC()
                scmD = getExposureScmD()
                scmE = spScmE
            }
            return@checkAnalysisContextReturn spScmE
        }
    }

    private fun track(scmE: String?) {
        SpmUtil.checkAnalysisContext(context) {
            val spm = it.getSpmCtnNewInstance()?.apply {
                spmC = getExposureSpmC()
                spmD = "${getExposureSpmD()}_btn@$clickType"
            }
            val scm = ScmBean().apply {
                scmA = getExposureScmA()
                scmB = getExposureScmB()
                scmC = getExposureScmC()
                scmD = "${getExposureScmD()}_text-$clickText"
                this.scmE = scmE
            }
        }
    }

    /**
     * 加购
     */
    fun trackAddCartClick() {
        clickType = REPORT_SUIXINPIN_BUTTON_CLICK_ADD_CART
        clickText = "加购"
        track(trackClickGoods())
    }

    /**
     * 加号
     */
    fun trackAddClick() {
        clickType = REPORT_SUIXINPIN_BUTTON_CLICK_ADD
        clickText = "加"
        track(trackClickGoods())
    }

    /**
     * 加购数量
     */
    fun trackAddNumberClick(count: String) {
        clickType = REPORT_SUIXINPIN_BUTTON_CLICK_ADD_NUMBER
        clickText = count
        track(trackClickGoods())
    }
}

/**
 * 随心拼View
 */
class SuiXinGoodsViewClick(context: Context, goodsInfo: IPaymentSuiXinPinGoods) :
    PaymentSuiXinGoodsClick(context, goodsInfo) {
    override fun getExposureSpmC(): String = "arbitraryList@5"
    override fun getExposureSpmD(): String = "prod@${goodsInfo.getRank()}"
    override fun logMessage(): String = "随心拼"
}

/**
 * 顺手买View
 */
class RecommendPayGoodsViewClick(context: Context, goodsInfo: IPaymentSuiXinPinGoods) :
    PaymentSuiXinGoodsClick(context, goodsInfo) {
    override fun getExposureSpmC(): String = "convenientList@5"
    override fun getExposureSpmD(): String = "prod@${goodsInfo.getRank()}"
    override fun logMessage(): String = "顺手买"
}

/**
 * 随心拼弹窗
 */
class RecommendPayGoodsViewPopWindowClick(context: Context, goodsInfo: IPaymentSuiXinPinGoods) :
    PaymentSuiXinGoodsClick(context, goodsInfo) {
    override fun getExposureSpmC(): String = "convenientList@5"
    override fun getExposureSpmD(): String = "ftFloatBuyMore@Z_prod@${goodsInfo.getRank()}"
    override fun logMessage(): String = "随心拼弹窗"
}

/**
 * 顺手买弹窗
 */
class SuiXinGoodsViewPopWindowClick(context: Context, goodsInfo: IPaymentSuiXinPinGoods) :
    PaymentSuiXinGoodsClick(context, goodsInfo) {
    override fun getExposureSpmC(): String = "arbitraryList@5"
    override fun getExposureSpmD(): String = "ftFloatBuyMore@Z_prod@${goodsInfo.getRank()}"
    override fun logMessage(): String = "随心拼弹窗"
}