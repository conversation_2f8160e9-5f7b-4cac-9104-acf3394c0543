<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="48dp">

        <ImageView
            android:id="@+id/ivPictureBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:src="@drawable/picture_back"
            android:padding="15dp" />

        <TextView
            android:id="@+id/tvPictureTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxEms="11"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:layout_centerInParent="true" />
    </RelativeLayout>


    <com.luck.picture.lib.widget.PreviewViewPager
        android:id="@+id/PreviewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

</LinearLayout>